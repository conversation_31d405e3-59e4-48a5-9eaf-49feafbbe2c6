'use client';

import { Control, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

interface IContactInfoFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function ContactInfoForm({ register, control, errors }: IContactInfoFormProps) {
  const gState = useGlobalState();

  return (
    <div>
      <FormTitle title="Contact Information" className="mt-6" />

      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* Mobile Number */}
        <FormField name="mobileNumber" label="Mobile No." errors={errors}>
          <Input
            {...register('mobileNumber', {
              required: false,
              validate: {
                isValidMobileNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid mobile number format (e.g. ***********)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.mobileNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="number"
            min={0}
            placeholder="Enter Mobile No."
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Tel Number */}
        <FormField name="telephoneNumber" label="Tel Number" errors={errors}>
          <Input
            {...register('telephoneNumber', {
              required: false,
              validate: {
                isValidTelephoneNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?(\d{2,4}[-\s]?)?\d{3,4}[-\s]?\d{4}$/.test(v || '') ||
                      'Invalid telephone number format'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.telephoneNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Tel Number"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Other Mobile Number */}
        <FormField name="otherMobileNumber" label="Other Mobile No." errors={errors}>
          <Input
            {...register('otherMobileNumber', {
              required: false,
              validate: {
                isValidMobileNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid mobile number format (e.g. ***********)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.otherMobileNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="number"
            min={0}
            placeholder="Enter Other Mobile No."
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Facebook Name */}
        <FormField name="facebookName" label="Facebook Name" errors={errors}>
          <Input
            {...register('facebookName')}
            className={cn(
              'focus-visible:ring-primary',
              errors.facebookName && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Facebook Name"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>
      </div>
    </div>
  );
}
