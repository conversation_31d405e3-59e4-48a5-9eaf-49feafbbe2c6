'use client';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';

import { FulfillmentTypeLabels, OrderStatusLabels } from '@/app/admin/marketplace/_components/Enums';

export const selectColumn = {
  id: 'select',
  header: ({ table }) => (
    <Checkbox
      checked={table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')}
      onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
      aria-label="Select all"
    />
  ),
  cell: ({ row }) => (
    <Checkbox
      checked={row.getIsSelected()}
      onCheckedChange={(value) => row.toggleSelected(!!value)}
      aria-label="Select row"
    />
  ),
  enableSorting: false,
  enableHiding: false,
};

export const columns = [
  {
    id: 'date_time',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${new Date(data.created_at).toLocaleDateString('en-US', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })} | ${new Date(data.created_at).toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return `${new Date(data.created_at).toLocaleDateString('en-US', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
      })}`;
    },
  },
  {
    id: 'order_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Order ID" />,
    accessorFn: (row) => `${row.reference_number}`,
  },
  {
    id: 'order_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Order Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Badge className={OrderStatusLabels[data.order_status].color}>
          {OrderStatusLabels[data.order_status].label}
        </Badge>
      );
    },
    accessorFn: (row) => {
      return `${OrderStatusLabels[row.order_status].label}`;
    },
  },
  {
    id: 'total_amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.total_price).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.total_price}`;
    },
  },
  {
    id: 'service_type',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Service Type" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{FulfillmentTypeLabels[data.fulfillment_type]}</div>;
    },
    accessorFn: (row) => {
      return `${FulfillmentTypeLabels[row.fulfillment_type]}`;
    },
  },
  {
    id: 'total_items',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Items" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.marketplaceProductOrders.map((invoice) => invoice.quantity).reduce((a, b) => a + b, 0)}`;
    },
  },
  {
    id: 'payment_method',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Method" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{['', 'Cash', 'e-Wallet', 'Multiple'][data.payment_method]}</div>;
    },
    accessorFn: (row) => {
      return `${['', 'Cash', 'e-Wallet', 'Multiple'][row.payment_method]}`;
    },
  },
];
