'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Input, InputSign } from '@/components/ui/input';

import useShuruCreditScoring from '@/lib/hooks/admin/useShuruCreditScoring';
import useShuruUtils from '@/lib/hooks/utils/useShuruUtils';
import { useCreditScoreState } from '@/lib/store/creditScore';
import { catchError, cn } from '@/lib/utils';

import { ErrorDialog } from './error-dialog';
import { SaveConfirmation } from './save-confirmation';

export default function BeforeLoan() {
  const shuruData = useShuruUtils();
  const { updateRules } = useShuruCreditScoring();
  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  const creditScore = useCreditScoreState();
  const beforeTotal = useHookstate(creditScore.before.total);
  const accountProfile = useHookstate(creditScore.before.accountProfile);
  const agricultureActivity = useHookstate(creditScore.before.agricultureActivity);
  const transactionRecords = useHookstate(creditScore.before.transactionRecords);
  const propertyOwnershipCondition = useHookstate(creditScore.before.propertyOwnership.conditions);

  const marketplaceComputation = useHookstate(creditScore.before.marketplaceComputation);
  const tradingPostComputation = useHookstate(creditScore.before.tradingPostComputation);
  const salesComputation = useHookstate(creditScore.before.salesComputation);

  const errorDialog = useHookstate(false);
  const confirmDialog = useHookstate(false);
  const isLoadingConfirm = useHookstate(false);

  const onSave = async () => {
    try {
      isLoadingConfirm.set(true);
      let dataToSubmit = [];

      // Account Profile
      const _accountProfile = shuruData.rules['before']['Account Profile'].get({ noproxy: true });
      delete _accountProfile.rule_category_score;
      _accountProfile.basicInformation.max_score = accountProfile.basicInfo.value;
      _accountProfile.careerAcademics.parent_data.parent_score = accountProfile.careerAcademic.value;
      _accountProfile.governmentIds.max_score = accountProfile.identificationDocuments.value;
      _accountProfile.familyProfile.max_score = accountProfile.familyProfile.value;
      _accountProfile.biometrics.max_score = accountProfile.biometrics.value;
      _accountProfile.propertyOwnership.max_score = accountProfile.propertyOwnership.value;
      _accountProfile.farmDetails.parent_data.parent_score = accountProfile.farmDetails.value;
      _accountProfile.vouchByLeader.max_score = accountProfile.vouchByLeaders.value;
      _accountProfile.vouchByMao.max_score = accountProfile.mao.value;

      _accountProfile.propertyOwnership.levels = _accountProfile.propertyOwnership.levels.map((lvl) => {
        const order = lvl['order'];
        const condition = propertyOwnershipCondition[order].get({ noproxy: true });
        let lower_bound =
          order === 1 ? lvl['lower_bound'] : propertyOwnershipCondition[order - 1]['loan'].get({ noproxy: true }) + 1;
        let upper_bound = order === 5 ? null : lvl['upper_bound'];

        return {
          ...lvl,
          score: condition['pts'],
          lower_bound,
          upper_bound,
        };
      });
      dataToSubmit.push(...Object.values(_accountProfile));

      // Agriculture Activity
      const _agricultureActivity = shuruData.rules['before']['Agriculture Activity'].get({ noproxy: true });
      delete _agricultureActivity.rule_category_score;
      _agricultureActivity.farmGeoTagging.max_score = agricultureActivity.geoTagging.value;
      _agricultureActivity.goodAgriculturalPractices.max_score = agricultureActivity.gapCompliance.value;
      dataToSubmit.push(...Object.values(_agricultureActivity));

      // Transaction Records
      const _transactionRecords = shuruData.rules['before']['Transaction Records'].get({ noproxy: true });
      delete _transactionRecords.rule_category_score;
      _transactionRecords.marketplaceTransaction.max_score = transactionRecords.marketplace.value;
      _transactionRecords.tradingPostTransaction.max_score = transactionRecords.tradingPost.value;
      _transactionRecords.saleOfHarvestToKita.max_score = transactionRecords.sales.value;

      _transactionRecords.marketplaceTransaction.levels = _transactionRecords.marketplaceTransaction.levels.map(
        (lvl) => {
          const order = lvl['order'];
          const condition = marketplaceComputation[order].get({ noproxy: true });
          let lower_bound =
            order === 1 ? condition['from'] : marketplaceComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
          let upper_bound = order === 5 ? null : condition['upto'];

          return {
            ...lvl,
            score: condition['pts'],
            lower_bound,
            upper_bound,
          };
        },
      );
      _transactionRecords.tradingPostTransaction.levels = _transactionRecords.tradingPostTransaction.levels.map(
        (lvl) => {
          const order = lvl['order'];
          const condition = tradingPostComputation[order].get({ noproxy: true });
          let lower_bound =
            order === 1 ? condition['from'] : tradingPostComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
          let upper_bound = order === 5 ? null : condition['upto'];

          return {
            ...lvl,
            score: condition['pts'],
            lower_bound,
            upper_bound,
          };
        },
      );
      _transactionRecords.saleOfHarvestToKita.levels = _transactionRecords.saleOfHarvestToKita.levels.map((lvl) => {
        const order = lvl['order'];
        const condition = salesComputation[order].get({ noproxy: true });
        let lower_bound =
          order === 1 ? condition['from'] : salesComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
        let upper_bound = order === 5 ? null : condition['upto'];

        return {
          ...lvl,
          score: condition['pts'],
          lower_bound,
          upper_bound,
        };
      });
      dataToSubmit.push(...Object.values(_transactionRecords));

      await updateRules(creditScoreGroupId, dataToSubmit);
    } catch (e) {
      catchError(e, 'onSave Before');
    } finally {
      isLoadingConfirm.set(false);
      confirmDialog.set(false);
    }
  };

  useEffect(
    () =>
      accountProfile.subscribe((v) => {
        beforeTotal.accountProfile.set(
          v.basicInfo +
            v.careerAcademic +
            v.identificationDocuments +
            v.familyProfile +
            v.biometrics +
            v.propertyOwnership +
            v.farmDetails +
            v.vouchByLeaders +
            v.mao,
        );
      }),
    [],
  );

  useEffect(
    () => agricultureActivity.subscribe((v) => beforeTotal.agricultureActivity.set(v.geoTagging + v.gapCompliance)),
    [],
  );

  useEffect(
    () =>
      transactionRecords.subscribe((v) => beforeTotal.transactionRecords.set(v.marketplace + v.tradingPost + v.sales)),
    [],
  );

  useEffect(
    () =>
      beforeTotal.subscribe((v) =>
        creditScore.total.before.set(v.accountProfile + v.agricultureActivity + v.transactionRecords),
      ),
    [],
  );

  return (
    <div className="grid gap-3">
      <div className="max-w-5xl">
        <Accordion type="single" collapsible className="w-full">
          {/* Account Profile */}
          <AccordionItem value="item-1">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Account Profile</h1>
                <div className="font-bold text-primary">{`${beforeTotal.accountProfile.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Basic Information</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.basicInfo.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.basicInfo.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Career & Academic</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.careerAcademic.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.careerAcademic.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Identification Documents</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.identificationDocuments.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.identificationDocuments.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Family Profile</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.familyProfile.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.familyProfile.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Biometrics</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.biometrics.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.biometrics.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Property Ownership</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.propertyOwnership.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.propertyOwnership.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl py-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 text-center focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          defaultValue={1}
                          disabled
                        />
                        <div>up to</div>
                        <Input
                          className="h-8 max-w-16 text-center"
                          placeholder="0"
                          min={2}
                          type="number"
                          value={propertyOwnershipCondition['1'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['1'].loan.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={propertyOwnershipCondition['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 text-center disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={propertyOwnershipCondition['1'].loan.value + 1}
                        />
                        <div>up to</div>
                        <Input
                          className="h-8 max-w-16 text-center"
                          placeholder="0"
                          min={4}
                          type="number"
                          value={propertyOwnershipCondition['2'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['2'].loan.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={propertyOwnershipCondition['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 text-center disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={propertyOwnershipCondition['2'].loan.value + 1}
                        />
                        <div>up to</div>
                        <Input
                          className="h-8 max-w-16 text-center"
                          placeholder="0"
                          min={6}
                          type="number"
                          value={propertyOwnershipCondition['3'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['3'].loan.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={propertyOwnershipCondition['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <Input
                          className="h-8 max-w-16 text-center disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={propertyOwnershipCondition['3'].loan.value + 1}
                        />
                        <div>up to</div>
                        <Input
                          className="h-8 max-w-16 text-center"
                          placeholder="0"
                          min={8}
                          type="number"
                          value={propertyOwnershipCondition['4'].loan.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['4'].loan.set(Number(e.target.value));
                            propertyOwnershipCondition['5'].loan.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={propertyOwnershipCondition['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            propertyOwnershipCondition['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-sm sm:items-center sm:justify-between">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <Input
                          className="h-8 max-w-16 text-center disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={propertyOwnershipCondition['5'].loan.value}
                        />
                      </div>

                      <div className="relative flex">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              propertyOwnershipCondition['5'].pts.value > accountProfile.propertyOwnership.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={propertyOwnershipCondition['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              propertyOwnershipCondition['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {propertyOwnershipCondition['5'].pts.value > accountProfile.propertyOwnership.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${accountProfile.propertyOwnership.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Farm Details</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.farmDetails.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.farmDetails.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Vouch from farmer cooperatives leader</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.vouchByLeaders.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.vouchByLeaders.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Municipal Agriculture Office (MAO)</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={accountProfile.mao.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      accountProfile.mao.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Agriculture Activity */}
          <AccordionItem value="item-2">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Agriculture Activity</h1>
                <div className="font-bold text-primary">{`${beforeTotal.agricultureActivity.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Farm Geo-Tagging</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.geoTagging.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.geoTagging.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Good Agricultural Practices (GAP) compliance</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.gapCompliance.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.gapCompliance.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Transaction Records */}
          <AccordionItem value="item-3">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Transaction Records</h1>
                <div className="font-bold text-primary">{`${beforeTotal.transactionRecords.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Marketplace Transaction</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.marketplace.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.marketplace.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['1'].from.value}
                          onChange={(e) => {
                            marketplaceComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['1'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['2'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['3'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['4'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['4'].upto.set(Number(e.target.value));
                            marketplaceComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-5 mt-3 grid gap-3 sm:ml-10">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative flex">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              marketplaceComputation['5'].pts.value > transactionRecords.marketplace.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={marketplaceComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              marketplaceComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {marketplaceComputation['5'].pts.value > transactionRecords.marketplace.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.marketplace.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Trading Post Transaction</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.tradingPost.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.tradingPost.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['1'].from.value}
                          onChange={(e) => {
                            tradingPostComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['1'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['2'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['3'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['4'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['4'].upto.set(Number(e.target.value));
                            tradingPostComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative flex">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              tradingPostComputation['5'].pts.value > transactionRecords.tradingPost.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={tradingPostComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              tradingPostComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {tradingPostComputation['5'].pts.value > transactionRecords.tradingPost.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.tradingPost.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Sales of Harvest to KITA</h1>
                <div className="flex shrink-0">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.sales.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.sales.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['1'].from.value}
                          onChange={(e) => {
                            salesComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['1'].upto.value}
                          onChange={(e) => {
                            salesComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['2'].upto.value}
                          onChange={(e) => {
                            salesComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['3'].upto.value}
                          onChange={(e) => {
                            salesComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['4'].upto.value}
                          onChange={(e) => {
                            salesComputation['4'].upto.set(Number(e.target.value));
                            salesComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative flex">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              salesComputation['5'].pts.value > transactionRecords.sales.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={salesComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              salesComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {salesComputation['5'].pts.value > transactionRecords.sales.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.sales.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="flex w-full max-w-5xl justify-between py-4">
          <h1 className="font-bold text-primary">Total</h1>
          <div className="font-bold text-primary">{`${creditScore.total.before.value}%`}</div>
        </div>

        <ErrorDialog state={errorDialog} />
        <SaveConfirmation state={confirmDialog} onSave={onSave} isLoading={isLoadingConfirm.value} />
      </div>

      <div className="mt-8">
        <Button
          className="px-12"
          onClick={() => {
            if (creditScore.total.before.value > 100 || creditScore.total.before.value < 100) {
              errorDialog.set(true);
              return;
            }

            confirmDialog.set(true);
          }}
        >
          Submit
        </Button>
      </div>
    </div>
  );
}
