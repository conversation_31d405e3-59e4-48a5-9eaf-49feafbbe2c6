'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

export default function LBPDataPrivacyForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Data Privacy Content LBP Form',
  });

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Data Privacy Content LBP Form</DialogTitle>
          </DialogHeader>
          <div className="mx-auto max-h-[50vh] max-w-4xl flex-1 flex-row justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef}>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/data-privacy-content-LBP-form.jpg)] bg-contain bg-top bg-no-repeat capitalize">
                <div className="absolute left-44 top-[56rem] uppercase">
                  {`${farmerData?.farmer?.first_name} ${farmerData?.farmer?.middle_name} ${farmerData?.farmer?.last_name}`}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
