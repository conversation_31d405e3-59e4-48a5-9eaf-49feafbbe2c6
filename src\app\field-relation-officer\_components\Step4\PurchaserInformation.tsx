import React from 'react';
import { Control, Controller, FieldErrors, useFormContext, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { OPTIONS_SOURCE_OF_FUNDS } from '@/app/admin/(accounts)/account-info/_components/tab-content/account-profile/Enums';
import { cn } from '@/lib/utils';

import { YES_NO_OPTIONS } from '../../constants';
import { TBusinessInfoSchema } from '../../schemas';

interface IPurchaserInformationProps {
  register: UseFormRegister<TBusinessInfoSchema>;
  control: Control<TBusinessInfoSchema>;
  errors: FieldErrors<TBusinessInfoSchema>;
}

const PurchaserInformation = ({ register, control, errors }: IPurchaserInformationProps) => {
  return (
    <div className="mt-6">
      <FormTitle title="Purchaser Information" />
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Selling Location for Harvested Crops */}
        <FormField name="purchaserSellingLocation" label="Selling Location for Harvested Crops" errors={errors}>
          <Input
            {...register('purchaserSellingLocation')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserSellingLocation && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Selling Location for Harvested Crops"
          />
        </FormField>

        {/* Buyer's Full Name */}
        <FormField name="purchaserFullname" label="Buyer's Full Name" errors={errors}>
          <Input
            {...register('purchaserFullname')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserFullname && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Full Name"
          />
        </FormField>

        {/* Buyer's Contact No. */}
        <FormField name="purchaserContactNumber" label="Buyer's Contact No." errors={errors}>
          <Input
            {...register('purchaserContactNumber')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserContactNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            onlyDigits
            inputMode="numeric"
            maxLength={11}
            type="text"
            placeholder="Enter Buyer's Contact No."
          />
        </FormField>

        <FormField
          name="isInterestedToSellAtTradingPost"
          label="Are you interested to sell at the Trading Post?"
          errors={errors}
          required
        >
          <Controller
            control={control}
            name="isInterestedToSellAtTradingPost"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>
    </div>
  );
};

export default PurchaserInformation;
