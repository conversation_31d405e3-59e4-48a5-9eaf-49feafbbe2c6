'use client';

import { useSearchParams } from 'next/navigation';
import React, { useEffect } from 'react';

import useFarmer from '@/lib/hooks/fro/useFarmer';

import AccountProfile from './_components/AccountProfile';
import Farmers from './_components/Farmers';

const Page = () => {
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const { getFarmerById } = useFarmer();

  useEffect(() => {
    if (id) {
      getFarmerById(id);
    }
  }, [id]);

  return <div>{id ? <AccountProfile /> : <Farmers />}</div>;
};

export default Page;
