'use client';

import { <PERSON><PERSON>inHouse, <PERSON>gyBankIcon, Shield<PERSON>heck, User } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { MdPassword } from 'react-icons/md';

import Stepper from '@/components/ui/stepper';

import useFarmer from '@/lib/hooks/fro/useFarmer';
import { useGlobalState } from '@/lib/store';

import DataPrivacy from '../_components/Step1/DataPrivacy';
import PersonalInfo from '../_components/Step2/PersonalInfo';
import FarmInformation from '../_components/Step3/FarmInformation';
import BusinessInfo from '../_components/Step4/BusinessInfo';

const steps = [
  { number: 1, label: 'Consent & Data Privacy', icon: <ShieldCheck /> },
  { number: 2, label: 'Personal Information', icon: <User /> },
  { number: 3, label: 'Farm Information', icon: <MapPinHouse /> },
  { number: 4, label: 'Financial & Business Info', icon: <PiggyBankIcon /> },
];

export default function KYC() {
  const { fro: gStateFRO } = useGlobalState();
  const { addFarmer } = useFarmer();
  const step = gStateFRO.form.activeStep;
  const currentStep = step.value;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [previousStep, setPreviousStep] = useState<number | null>(null);

  const handleNext = () => {
    setPreviousStep(currentStep);
    step.set(currentStep + 1);
    // Scroll the scroll area container to top
    const scrollArea = document.querySelector('[data-radix-scroll-area-viewport]');
    if (scrollArea) {
      scrollArea.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handlePrev = () => {
    setPreviousStep(currentStep);
    step.set(Math.max(currentStep - 1, 1));
    // Scroll the scroll area container to top
    const scrollArea = document.querySelector('[data-radix-scroll-area-viewport]');
    if (scrollArea) {
      scrollArea.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const clearAllForms = () => {
    gStateFRO.form.step1.set(null);
    gStateFRO.form.step2.set(null);
    gStateFRO.form.step3.set(null);
    gStateFRO.form.step4.set(null);
    step.set(1);
  };

  const gStateSteps = [
    gStateFRO.form.step1.value,
    gStateFRO.form.step2.value,
    gStateFRO.form.step3.value,
    gStateFRO.form.step4.value,
  ];
  const allStepsFilled = gStateSteps.every((step) => step !== null && step !== undefined);

  const handleSaveAll = async () => {
    setIsLoading(true);

    const data = Object.assign({}, ...gStateSteps);

    // NOTE: temporarily commented for QA
    // const _data = {
    //   ...gStateFRO.form.step1.value,
    //   ...gStateFRO.form.step2.value,
    //   ...gStateFRO.form.step3.value,
    //   ...gStateFRO.form.step4.value,
    // };

    const governmentIdUploads: Record<string, File> = {};

    if (Array.isArray(data.governmentIdentification)) {
      data.governmentIdentification.forEach((row: any) => {
        const key = `governmentIdentification_${row.governmentIdNumber}`;

        if (row.upload && row.upload[0]) {
          governmentIdUploads[key] = row.upload[0];
        } else if (row.upload instanceof File) {
          governmentIdUploads[key] = row.upload;
        }
      });
    }

    // Handle "Others" fields by replacing "Others" with the actual value
    const processFieldWithOthers = (fieldData: string[] | string | undefined, othersValue: string) => {
      if (!fieldData) return '';

      // Convert to array if it's a string
      const fieldArray = Array.isArray(fieldData)
        ? fieldData
        : typeof fieldData === 'string'
          ? fieldData.split(',').map((item) => item.trim())
          : [];

      if (fieldArray.length === 0) return '';

      const processedArray = fieldArray.map((item) => (item === 'Others' && othersValue ? othersValue : item));
      return processedArray.join(', ');
    };

    // Helper function to check if field has data
    const hasData = (field: any) => {
      if (Array.isArray(field)) return field.length > 0;
      if (typeof field === 'string') return field.trim().length > 0;
      return false;
    };

    // Helper function to safely process arrays
    const safeArrayProcess = (field: any, processor: (arr: string[]) => string) => {
      if (Array.isArray(field)) return processor(field);
      if (typeof field === 'string') return processor(field.split(',').map((item) => item.trim()));
      return '';
    };

    const payload = {
      ...data,
      ...governmentIdUploads,
      // attachment: data.attachment[0] || data.attachment,
      ...(hasData(data.pastFarmLoans) && {
        pastFarmLoans: safeArrayProcess(data.pastFarmLoans, (arr) => arr.join(', ')),
      }),
      ...(hasData(data.needFarmLoanReason) && {
        needFarmLoanReason: safeArrayProcess(data.needFarmLoanReason, (arr) => arr.join(', ')),
      }),
      ...(hasData(data.farmImplements) && {
        farmImplements: processFieldWithOthers(data.farmImplements, data.farmImplementsOthers),
      }),
      ...(hasData(data.waterSource) && {
        waterSource: processFieldWithOthers(data.waterSource, data.waterSourceOthers),
      }),
      ...(hasData(data.sourceOfFunds) && {
        sourceOfFunds: processFieldWithOthers(data.sourceOfFunds, data.sourceOfFundsOthers),
      }),
      userImage: data.userImage?.[0] || data.userImage,
      cropsPlanted: Array.isArray(data.cropsPlanted)
        ? data.cropsPlanted.map((item: string) => item.split('-')[0].trim())
        : [],
    };
    console.log('all data:', { payload, data });

    await addFarmer(payload)
      .then((res) => {
        if (res?.status === 1) {
          clearAllForms();
        }
      })
      .finally(() => setIsLoading(false));
  };

  const handleClearForms = () => {
    const confirmClear = window.confirm('Are you sure you want to clear all form data? This action cannot be undone.');

    if (!confirmClear) return;
    clearAllForms();
  };

  useEffect(() => {
    if (!currentStep) {
      step.set(1);
    }
  }, []);

  console.log('gStateFRO', { values: gStateFRO.form.value, allStepsFilled });

  const renderStepForm = () => {
    switch (currentStep) {
      case 1:
        return (
          <DataPrivacy
            gStateFRO={gStateFRO}
            onNext={handleNext}
            onClear={handleClearForms}
            fromPreviousStep={previousStep === 2}
          />
        );
      case 2:
        return (
          <PersonalInfo gStateFRO={gStateFRO} onNext={handleNext} onPrevious={handlePrev} onClear={handleClearForms} />
        );
      case 3:
        return (
          <FarmInformation
            gStateFRO={gStateFRO}
            onNext={handleNext}
            onPrevious={handlePrev}
            onClear={handleClearForms}
          />
        );
      case 4:
        return (
          <BusinessInfo
            gStateFRO={gStateFRO}
            onPrevious={handlePrev}
            onClear={handleClearForms}
            onSave={handleSaveAll}
            isLoading={isLoading}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-2 md:container">
      <Stepper
        steps={steps}
        currentStep={currentStep}
        onStepClick={allStepsFilled ? (v) => step.set(v) : undefined}
        hasIndicator={true}
      />
      <div className="mt-4">{renderStepForm()}</div>
    </div>
  );
}
