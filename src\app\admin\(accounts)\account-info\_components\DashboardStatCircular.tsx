'use client';

import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';

import CircularProgress from './CircularProgress';

export const DashboardStatCircular = ({
  name = '',
  leftTab,
  circularBg = '',
  circularColor = '',
  value = 0,
  tabValue = '',
  buttonBg = '',
  btnClassName = '',
  total = 100,
}) => {
  return (
    <div className="grid grid-cols-1 place-items-center">
      <CircularProgress className={circularBg} progressClassName={circularColor} percentage={value} total={total} />

      <div className="mt-6 flex justify-center">
        <Button
          className={cn(
            'transition duration-300 ease-in-out',
            'rounded-none hover:text-white font-bold text-xs',
            leftTab.value === tabValue ? buttonBg : 'bg-transparent text-gray-800',
            btnClassName,
          )}
          onClick={() => leftTab.set(tabValue)}
        >
          {name}
        </Button>
      </div>
    </div>
  );
};
