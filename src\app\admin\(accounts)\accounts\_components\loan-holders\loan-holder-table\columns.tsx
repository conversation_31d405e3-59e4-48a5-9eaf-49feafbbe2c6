'use client';

import { none, useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { SlidersHorizontalIcon } from 'lucide-react';
import { CSVLink } from 'react-csv';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Slider } from '@/components/ui/slider';

import { getLoanHolderStatus } from '@/lib/constants/enums';
import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useMember from '@/lib/hooks/admin/useMember';
import { useGlobalState } from '@/lib/store';
import { cn, formatNumber } from '@/lib/utils';

export const LoanHolderColumns = [
  {
    id: 'payment_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const status = getLoanHolderStatus(data);
      return (
        <div className="min-w-max">
          <Badge className={cn('min-w-[100px] justify-center', status.color)}>{status.name}</Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      const status = getLoanHolderStatus(row);
      return `${row.topupRequest.payment_status}-${status.name}`;
    },
  },
  {
    id: 'cs_group',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Credit Score Group" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.topupRequest.creditScoreGroup.name}</div>;
    },
    accessorFn: (row) => {
      return row.topupRequest.creditScoreGroup.name;
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.user.farmer.first_name} {data.user.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.user.farmer.first_name} ${row.user.farmer.last_name}`;
    },
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => {
      return `ID${row.user.id.toString().padStart(9, '0')}`;
    },
  },
  {
    id: 'loan_amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Amount" />,
    accessorFn: (row) => {
      return Number(row.topupRequest.total_loan_amount).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      });
    },
  },
  {
    id: 'loan_balance',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Balance" />,
    accessorFn: (row) => {
      return Number(row.topupRequest.total_loan_amount - row.user.wallet.payment).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      });
    },
  },
  {
    id: 'loan_term',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Term" />,
    accessorFn: (row) => {
      return `${row.topupRequest.loan_term} days`;
    },
  },
  {
    id: 'due_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Due Date" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.topupRequest.due_at ? format(new Date(data.topupRequest.due_at), 'MMM dd, yyyy') : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.topupRequest.due_at ? format(new Date(row.topupRequest.due_at), 'MMM dd, yyyy') : '-';
    },
  },
  {
    id: 'loan_cycle',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Cycle" />,
    accessorFn: (row) => {
      return row.loan_cyle;
    },
  },
  {
    id: 'approved_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Approved By" />,
    accessorFn: (row) => {
      return `${row.topupRequest.approvedBy.finance.first_name} ${row.topupRequest.approvedBy.finance.last_name}`;
    },
  },
];

const exportHeaders = [
  { label: 'Payment Status', key: 'paymentStatus' },
  { label: 'Credit Score Group', key: 'lender' },
  { label: 'Name', key: 'name' },
  { label: 'Account ID', key: 'userId' },
  { label: 'Loan Amount', key: 'loanAmount' },
  { label: 'Loan Balance', key: 'loanBalance' },
  { label: 'Loan Term', key: 'loanTerm' },
  { label: 'Due Date', key: 'dueAt' },
  { label: 'Loan Cycle', key: 'loanCycle' },
  { label: 'Approved By', key: 'approvedBy' },
];

export const loanHolderPaymentStatuses = [
  {
    id: 0,
    name: 'Active',
    value: 'ACTIVE',
    color: 'bg-[#49C272] hover:bg-[#49C272]/90 text-white',
  },
  {
    id: 3,
    name: 'In Grace Period',
    value: 'GRACE',
    color: 'bg-[#FF8831] hover:bg-[#FF8831]/90 text-white',
  },
  {
    id: 1,
    name: 'Paid Off',
    value: 'PAID',
    color: 'bg-[#5080FF] hover:bg-[#5080FF]/90 text-white',
  },
  {
    id: 2,
    name: 'Overdue',
    value: 'OVERDUE',
    color: 'bg-[#DA5043] hover:bg-[#DA5043]/90 text-white',
  },
];

export function LoanHolderActionHeader() {
  const gState = useGlobalState();
  const { loanHolders, paginationLoanHolder } = useMember();
  const { groups } = useCreditScoreMgt();

  const loanBalMinMax = useHookstate([0, 9999999]);
  const loanAmountMinMax = useHookstate([0, 9999999]);

  const getData = () => {
    const data = loanHolders.data.get({ noproxy: true });
    return (data as any[]).map((row) => {
      const status = getLoanHolderStatus(row);

      return {
        paymentStatus: status.name,
        lender: row.topupRequest.creditScoreGroup.name,
        name: `${row.user.farmer.first_name} ${row.user.farmer.last_name}`,
        userId: row.user.id,
        loanAmount: Number(row.topupRequest.total_loan_amount).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        loanBalance: Number(row.topupRequest.total_loan_amount - row.user.wallet.payment).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        }),
        loanTerm: `${row.topupRequest.loan_term} days`,
        dueAt: row.topupRequest.due_at ? format(new Date(row.topupRequest.due_at), 'MMM dd, yyyy') : '-',
        loanCycle: row.loan_cyle,
        approvedBy: `${row.topupRequest.approvedBy.finance.first_name} ${row.topupRequest.approvedBy.finance.last_name}`,
      };
    });
  };

  const onReset = () => {
    paginationLoanHolder.creditScoreGroupIds.set([]);
    paginationLoanHolder.paymentStatuses.set([]);
    paginationLoanHolder.loanTerm.set(120);
    paginationLoanHolder.loanBalanceStart.set(0);
    paginationLoanHolder.loanBalanceEnd.set(9999999);
    paginationLoanHolder.loanAmountStart.set(0);
    paginationLoanHolder.loanAmountEnd.set(9999999);

    loanBalMinMax.set([0, 9999999]);
    loanAmountMinMax.set([0, 9999999]);
  };

  return (
    <div className="flex items-center gap-2">
      <Sheet>
        <SheetTrigger asChild>
          <Button className="h-8" variant="outline" size="sm">
            <SlidersHorizontalIcon className="mr-2 size-4" />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent className="flex flex-col p-0">
          <ScrollArea className="h-1 flex-1 px-6">
            <SheetHeader className="pt-6">
              <div className="flex items-center justify-between pt-4">
                <SheetTitle>Filter</SheetTitle>
                <SheetTitle>
                  <Button onClick={onReset} className="text-base text-kitaph-primary" variant="ghost">
                    Reset All
                  </Button>
                </SheetTitle>
              </div>
            </SheetHeader>

            <div className="pb-6">
              <div className="mb-2 mt-4 text-sm text-gray-500">Financing / Lender</div>

              {groups
                .get({ noproxy: true })
                .filter((f) => f.status === 1)
                .map((group) => {
                  return (
                    <div key={`${group.id}`} className="flex items-center gap-2 py-2">
                      <Checkbox
                        id={`lender-${group.id}`}
                        checked={paginationLoanHolder.creditScoreGroupIds.value.includes(group.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            paginationLoanHolder.creditScoreGroupIds.merge([group.id]);
                          } else {
                            const _findIndex = paginationLoanHolder.creditScoreGroupIds.value.findIndex(
                              (f) => f === group.id,
                            );
                            if (_findIndex !== -1) {
                              paginationLoanHolder.creditScoreGroupIds[_findIndex].set(none);
                            }
                          }
                        }}
                      />
                      <label
                        htmlFor={`lender-${group.id}`}
                        className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {group.name}
                      </label>
                    </div>
                  );
                })}

              <div className="mb-2 mt-4 text-sm text-gray-500">Payment Status</div>

              {loanHolderPaymentStatuses.map((status) => {
                return (
                  <div key={`${status.id}`} className="flex items-center gap-2 py-2">
                    <Checkbox
                      id={`paymentStatus-${status.id}`}
                      checked={paginationLoanHolder.paymentStatuses.value.includes(status.value)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          paginationLoanHolder.paymentStatuses.merge([status.value]);
                        } else {
                          const _findIndex = paginationLoanHolder.paymentStatuses.value.findIndex(
                            (f) => f === status.value,
                          );
                          if (_findIndex !== -1) {
                            paginationLoanHolder.paymentStatuses[_findIndex].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={`paymentStatus-${status.id}`}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {status.name}
                    </label>
                  </div>
                );
              })}

              <div className="mb-2 mt-4 text-sm text-gray-500">Loan Term</div>

              <div className="flex flex-wrap gap-4 py-2">
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    paginationLoanHolder.loanTerm.value === 30
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => paginationLoanHolder.loanTerm.set(30)}
                >{`< 30 days`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    paginationLoanHolder.loanTerm.value === 60
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => paginationLoanHolder.loanTerm.set(60)}
                >{`< 60 days`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    paginationLoanHolder.loanTerm.value === 90
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => paginationLoanHolder.loanTerm.set(90)}
                >{`< 90 days`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    paginationLoanHolder.loanTerm.value === 120
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => paginationLoanHolder.loanTerm.set(120)}
                >{`< 120 days`}</Button>
                <Button
                  className={cn(
                    'h-8 rounded-full overflow-hidden',
                    paginationLoanHolder.loanTerm.value === 150
                      ? 'bg-kitaph-primary text-white hover:bg-kitaph-primary-600'
                      : '',
                  )}
                  size="sm"
                  variant="secondary"
                  onClick={() => paginationLoanHolder.loanTerm.set(150)}
                >{`< 150 days`}</Button>
              </div>

              <div className="mb-2 mt-4 text-sm text-gray-500">Loan Balance</div>

              <div className="grid gap-4 py-2">
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={0}
                    max={loanBalMinMax.value[1] - 1}
                    value={loanBalMinMax.value[0]}
                    onChange={(e) => {
                      loanBalMinMax[0].set(Number(e.target.value));
                      paginationLoanHolder.loanBalanceStart.set(Number(e.target.value));
                    }}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={loanBalMinMax.value[0] + 1}
                    value={loanBalMinMax.value[1]}
                    onChange={(e) => {
                      loanBalMinMax[1].set(Number(e.target.value));
                      paginationLoanHolder.loanBalanceEnd.set(Number(e.target.value));
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-sm font-bold text-blue-500">
                  <div>PHP {formatNumber(paginationLoanHolder.loanBalanceStart.value)}</div>
                  <div>PHP {formatNumber(paginationLoanHolder.loanBalanceEnd.value)}</div>
                </div>

                <Slider
                  min={loanBalMinMax.value[0]}
                  max={loanBalMinMax.value[1]}
                  value={[paginationLoanHolder.loanBalanceStart.value, paginationLoanHolder.loanBalanceEnd.value]}
                  onValueChange={(v) => {
                    paginationLoanHolder.loanBalanceStart.set(v[0]);
                    paginationLoanHolder.loanBalanceEnd.set(v[1]);
                  }}
                />
              </div>

              <div className="mb-2 mt-4 text-sm text-gray-500">Loan Amount</div>

              <div className="grid gap-4 py-2">
                <div className="grid grid-cols-2 gap-3">
                  <Input
                    type="number"
                    placeholder="Min"
                    min={0}
                    max={loanAmountMinMax.value[1] - 1}
                    value={loanAmountMinMax.value[0]}
                    onChange={(e) => {
                      loanAmountMinMax[0].set(Number(e.target.value));
                      paginationLoanHolder.loanAmountStart.set(Number(e.target.value));
                    }}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    min={loanAmountMinMax.value[0] + 1}
                    value={loanAmountMinMax.value[1]}
                    onChange={(e) => {
                      loanAmountMinMax[1].set(Number(e.target.value));
                      paginationLoanHolder.loanAmountEnd.set(Number(e.target.value));
                    }}
                  />
                </div>

                <div className="flex items-center justify-between text-sm font-bold text-blue-500">
                  <div>PHP {formatNumber(paginationLoanHolder.loanAmountStart.value)}</div>
                  <div>PHP {formatNumber(paginationLoanHolder.loanAmountEnd.value)}</div>
                </div>

                <Slider
                  min={loanAmountMinMax.value[0]}
                  max={loanAmountMinMax.value[1]}
                  value={[paginationLoanHolder.loanAmountStart.value, paginationLoanHolder.loanAmountEnd.value]}
                  onValueChange={(v) => {
                    paginationLoanHolder.loanAmountStart.set(v[0]);
                    paginationLoanHolder.loanAmountEnd.set(v[1]);
                  }}
                />
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>

      <Button size="sm" className="h-8" asChild>
        <CSVLink
          data={getData()}
          headers={exportHeaders}
          filename={`loan-holders-${format(new Date(), 'yyyy-MM-dd')}.csv`}
          target="_blank"
        >
          Export
        </CSVLink>
      </Button>
    </div>
  );
}
