export const LOAN_REQUIREMENTS_STAGE = {
  1: 'Stage 1 - Farmer Enrollment',
  2: 'Stage 2 - Third Party Documents & Loan Application Completion',
  3: 'Stage 3 - Loan Approval',
  4: 'Stage 4 - Loan Release and Fund Transfer',
};

export const LOAN_REQ_ITEMS = {
  // Stage 1
  '2x2': '2x2 Picture',
  bir_tin: 'BIR TIN',
  rsbsa_id: 'RSBSA ID',
  government_id: 'Government Issued ID',
  barangay_clearance: 'Barangay Clearance',
  land_agreement: 'Photocopy of Land Agreement / Lease Agreement',
  kita_loan_form: 'Kita Loan Form',
  lbp_agrisenso_loan_form: 'LBP Agrisenso Loan Form',
  lbp_data_privacy: 'LBP Data Privacy and CIS Form',
  farmer_meeting: 'Farmers Meeting / Financial Literacy',

  // Stage 2
  nia_certification: 'NIA Certification',
  ia_certification: 'IA Certification',
  dar_certification: 'DAR Certification',
  mao_certification: 'MAO Certification',
  pcic: 'Geo Tagging / Farm Picture / PCIC',
  ptma: 'PTMA',
  msa: 'MSA',
  farm_plan: 'Farm Plan',
  tripartite_agreement: 'Tripartite Agreement',
  signed_notarized_documents: 'Signed & Notarized Documents',

  // Stage 3
  submission_of_documents: 'Submission of Documents to LBP',
  verification_of_documents: 'Verification / Evaluation of Documents',
  promissory_note_signing: 'Promissory Note Signing',
  opening_of_account: 'Opening of LBP Account',

  // Stage 4
  agri_inputs_distribution: 'Agri-inputs Distribution to Farmers',
  submission_of_soa: 'Submission of Delivery Receipts / Sales Invoice / Statement of Account to LBP',
  transfer_of_cash: 'Transfer of Cash to Kita Bank Account from Farmers Account',
  amount_to_be_credited: 'Amount to be Credited to Kita Bank Account',
};

/*
 * ----------------------------------------------
 * View Details
 * ----------------------------------------------
 */
export interface IViewDetailsLoanRequirementResponse {
  status: number;
  data: IViewDetailsLoanRequirementData[];
}

export interface IViewDetailsLoanRequirementData {
  id: number;
  farmer_id: number;
  stage: number;
  stage_summary: IStageSummary;
  stage_count: number;
  stage_started_at: null | string;
  stage_completed_at: null | string;
  created_at: string;
  updated_at: string;
  farmerLoanRequirementItems: IFarmerLoanRequirementItems[];
}

export interface IFarmerLoanRequirementItems {
  id: number;
  farmer_id: number;
  processed_by_id: null | number;
  farmer_loan_requirement_id: number;
  name: string;
  stage: number;
  attachment: null | string;
  notes: null | string;
  is_completed: number;
  created_at: string;
  updated_at: string;
}

/*
 * ----------------------------------------------
 * Loan Requirements Table
 * ----------------------------------------------
 */

export interface ILoanRequirementsResponse {
  status: number;
  data: ILoanRequirement[];
}

export interface ILoanRequirement {
  id: number;
  farmer_id: number;
  stage: number;
  stage_summary: IStageSummary;
  stage_count: number;
  stage_started_at: null | string;
  stage_completed_at: null | string;
  created_at: string;
  updated_at: string;
}

export interface IStageSummary {
  msa?: boolean;
  pcic?: boolean;
  ptma?: boolean;
  farm_plan?: boolean;
  ia_certification?: boolean;
  dar_certification?: boolean;
  mao_certification?: boolean;
  nia_certification?: boolean;
  tripartite_agreement?: boolean;
  signed_notarized_documents?: boolean;
  opening_of_account?: boolean;
  promissory_note_signing?: boolean;
  submission_of_documents?: boolean;
  verification_of_documents?: boolean;
  transfer_of_cash?: boolean;
  submission_of_soa?: boolean;
  amount_to_be_credited?: boolean;
  agri_inputs_distribution?: boolean;
  '2x2'?: boolean;
  bir_tin?: boolean;
  rsbsa_id?: boolean;
  government_id?: boolean;
  farmer_meeting?: boolean;
  kita_loan_form?: boolean;
  land_agreement?: boolean;
  lbp_data_privacy?: boolean;
  barangay_clearance?: boolean;
  lbp_agrisenso_loan_form?: boolean;
}
