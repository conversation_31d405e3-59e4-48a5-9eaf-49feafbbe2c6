'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { LoanRequestStatus } from '@/app/admin/marketplace/_components/Enums';
import { useGlobalState } from '@/lib/store';
import { marketplaceReportsState } from '@/lib/store/marketplace-reports-store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function ReportsTable({ columns, data, meta = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const pagination = useHookstate(marketplaceReportsState.query);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);

        gStateP.admin['viewOrder'].set(data);
        router.push('/admin/marketplace/order/details?id=' + data.id);
      },
    },
  });

  return (
    <div className="space-y-4">
      <DataTableToolbar id="marketplace-reports" table={table} meta={meta} />

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      const target = event.target as HTMLElement;
                      const isButton = target.tagName.toLowerCase() === 'button';
                      const isP = target.tagName.toLowerCase() === 'p';
                      const isHeading = target.tagName.toLowerCase() === 'h2';
                      const isOpen = target.dataset.state === 'open';
                      const isDisabled = target.classList.contains('flex');

                      if (!isButton && !isP && !isHeading && !isOpen && !isDisabled) {
                        table.options.meta?.getRowClicked?.(row);
                      }
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {meta ? (
        <DataTablePaginationMeta
          table={table}
          meta={meta}
          onChangePageSize={(pageSize) => {
            pagination.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            pagination.page.set(page);
          }}
          all={meta.total}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
