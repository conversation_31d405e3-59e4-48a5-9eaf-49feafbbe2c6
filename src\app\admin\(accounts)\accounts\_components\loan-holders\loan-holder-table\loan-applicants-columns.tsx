'use client';

import { none, useHookstate } from '@hookstate/core';
import { SlidersHorizontalIcon } from 'lucide-react';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

import {
  EFarmerLoanRequirementItemStage,
  LoanApplicationLabels,
  LoanApplicationStageLabels,
  stageFourLoanRequirements,
  stageOneLoanRequirements,
  stageThreeLoanRequirements,
  stageTwoLoanRequirements,
} from '@/lib/constants/enums';
import useMember from '@/lib/hooks/admin/useMember';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

const stageRequirementCountMap: Record<EFarmerLoanRequirementItemStage, number> = {
  [EFarmerLoanRequirementItemStage.STAGE_ONE]: stageOneLoanRequirements.length,
  [EFarmerLoanRequirementItemStage.STAGE_TWO]: stageTwoLoanRequirements.length,
  [EFarmerLoanRequirementItemStage.STAGE_THREE]: stageThreeLoanRequirements.length,
  [EFarmerLoanRequirementItemStage.STAGE_FOUR]: stageFourLoanRequirements.length,
};

export const LoanApplicantsColumns = [
  {
    id: 'loan_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Stage" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          <Badge
            className={cn(
              'min-w-[100px] justify-center',
              LoanApplicationStageLabels[data.loan_application_stage].color,
            )}
          >
            {LoanApplicationStageLabels[data.loan_application_stage].label}
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      return LoanApplicationStageLabels[row.status].label;
    },
  },
  {
    id: 'completion_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Completion Status" />,
    cell: ({ row }) => {
      const data = row.original;
      const totalRequirements = stageRequirementCountMap[data.loan_application_stage] || 0;
      return (
        <div className="min-w-max">
          {totalRequirements > 0 ? (
            <div>
              {data?.loan_application_completion_count} / {totalRequirements}
            </div>
          ) : (
            'N/A'
          )}
        </div>
      );
    },
    accessorFn: (row) => {
      const totalRequirements = stageRequirementCountMap[row.loan_application_stage] || 0;
      return totalRequirements > 0 ? row.loan_application_completion_count / totalRequirements : 0;
    },
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.user.farmer.first_name} {data.user.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.user.farmer.first_name} ${row.user.farmer.last_name}`;
    },
  },
  {
    id: 'location',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Location" />,
    accessorFn: (row) => {
      try {
        const address = JSON.parse(row?.user?.farmer?.address || '{}');
        const province = JSON.parse(address?.addressProvince || '{}');
        return (address && province?.province_name) || 'N/A';
      } catch (e) {
        return '';
      }
    },
  },
  {
    id: 'submitted_loans_to',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Submitted Loans to" />,
    accessorFn: (row) => {
      return row.credit_score_groups || 'N/A';
    },
  },
];

export function LoanApplicantsActionHeader() {
  const { paginationLoanApplicants } = useMember();

  const onReset = () => {
    paginationLoanApplicants.requirements.set([]);
  };

  return (
    <div className="flex items-center gap-2">
      <Sheet>
        <SheetTrigger asChild>
          <Button className="h-8" variant="outline" size="sm">
            <SlidersHorizontalIcon className="mr-2 size-4" />
            Filter
          </Button>
        </SheetTrigger>
        <SheetContent className="flex flex-col p-0">
          <ScrollArea className="h-1 flex-1 px-6">
            <SheetHeader className="pt-6">
              <div className="flex items-center justify-between pt-4">
                <SheetTitle>Filter</SheetTitle>
                <SheetTitle>
                  <Button onClick={onReset} className="text-base text-kitaph-primary" variant="ghost">
                    Reset All
                  </Button>
                </SheetTitle>
              </div>

              <div className="pb-6">
                <div className="mb-2 mt-4 text-sm text-gray-500">Unsubmitted Requirements</div>

                <div className="mb-2 mt-4 text-sm text-gray-500">Stage 1</div>

                {stageOneLoanRequirements.map(({ slug, name }) => (
                  <div key={slug} className="flex items-start gap-2 py-2">
                    <Checkbox
                      id={slug}
                      checked={paginationLoanApplicants.requirements.value.includes(slug)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          paginationLoanApplicants.requirements.merge([slug]);
                        } else {
                          const index = paginationLoanApplicants.requirements.value.findIndex((r) => r === slug);
                          if (index !== -1) {
                            paginationLoanApplicants.requirements[index].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={slug}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {name}
                    </label>
                  </div>
                ))}

                <div className="mb-2 mt-4 text-sm text-gray-500">Stage 2</div>

                {stageTwoLoanRequirements.map(({ slug, name }) => (
                  <div key={slug} className="flex items-start gap-2 py-2">
                    <Checkbox
                      id={slug}
                      checked={paginationLoanApplicants.requirements.value.includes(slug)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          paginationLoanApplicants.requirements.merge([slug]);
                        } else {
                          const index = paginationLoanApplicants.requirements.value.findIndex((r) => r === slug);
                          if (index !== -1) {
                            paginationLoanApplicants.requirements[index].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={slug}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {name}
                    </label>
                  </div>
                ))}

                <div className="mb-2 mt-4 text-sm text-gray-500">Stage 3</div>

                {stageThreeLoanRequirements.map(({ slug, name }) => (
                  <div key={slug} className="flex items-start gap-2 py-2">
                    <Checkbox
                      id={slug}
                      checked={paginationLoanApplicants.requirements.value.includes(slug)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          paginationLoanApplicants.requirements.merge([slug]);
                        } else {
                          const index = paginationLoanApplicants.requirements.value.findIndex((r) => r === slug);
                          if (index !== -1) {
                            paginationLoanApplicants.requirements[index].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={slug}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {name}
                    </label>
                  </div>
                ))}

                <div className="mb-2 mt-4 text-sm text-gray-500">Stage 4</div>

                {stageFourLoanRequirements.map(({ slug, name }) => (
                  <div key={slug} className="flex items-start gap-2 py-2">
                    <Checkbox
                      id={slug}
                      checked={paginationLoanApplicants.requirements.value.includes(slug)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          paginationLoanApplicants.requirements.merge([slug]);
                        } else {
                          const index = paginationLoanApplicants.requirements.value.findIndex((r) => r === slug);
                          if (index !== -1) {
                            paginationLoanApplicants.requirements[index].set(none);
                          }
                        }
                      }}
                    />
                    <label
                      htmlFor={slug}
                      className="mr-4 flex flex-1 cursor-pointer items-center justify-between gap-4 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {name}
                    </label>
                  </div>
                ))}
              </div>
            </SheetHeader>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </div>
  );
}
