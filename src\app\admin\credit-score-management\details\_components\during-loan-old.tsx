'use client';

import { useHookstate } from '@hookstate/core';
import { parseAsInteger, useQueryState } from 'nuqs';
import { useEffect } from 'react';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { InputSign } from '@/components/ui/input';

import useShuruCreditScoring from '@/lib/hooks/admin/useShuruCreditScoring';
import useShuruUtils from '@/lib/hooks/utils/useShuruUtils';
import { useCreditScoreState } from '@/lib/store/creditScore';
import { catchError, cn } from '@/lib/utils';

import { ErrorDialog } from './error-dialog';
import { SaveConfirmation } from './save-confirmation';

export default function DuringLoan() {
  const shuruData = useShuruUtils();
  const { updateRules } = useShuruCreditScoring();
  const [creditScoreGroupId, setCreditScoreGroupId] = useQueryState('id', parseAsInteger);

  const creditScore = useCreditScoreState();
  const duringTotal = useHookstate(creditScore.during.total);
  const agricultureActivity = useHookstate(creditScore.during.agricultureActivity);
  const transactionRecords = useHookstate(creditScore.during.transactionRecords);

  const marketplaceComputation = useHookstate(creditScore.during.marketplaceComputation);
  const tradingPostComputation = useHookstate(creditScore.during.tradingPostComputation);
  const salesComputation = useHookstate(creditScore.during.salesComputation);

  const fertilizationSchedule = useHookstate(creditScore.during.fertilizationSchedule);
  const fertilizationVolume = useHookstate(creditScore.during.fertilizationVolume);
  const cropProtectionSchedule = useHookstate(creditScore.during.cropProtectionSchedule);
  const cropProtectionVolume = useHookstate(creditScore.during.cropProtectionVolume);

  const fertilizationScheduleError = useHookstate(false);
  const fertilizationVolumeError = useHookstate(false);
  const cropProtectionScheduleError = useHookstate(false);
  const cropProtectionVolumeError = useHookstate(false);

  const errorDialog = useHookstate(false);
  const confirmDialog = useHookstate(false);
  const isLoadingConfirm = useHookstate(false);

  const onSave = async () => {
    try {
      isLoadingConfirm.set(true);
      let dataToSubmit = [];

      // Agriculture Activity
      const _agricultureActivity = shuruData.rules['during']['Agriculture Activity'].get({ noproxy: true });
      delete _agricultureActivity.rule_category_score;
      _agricultureActivity.landPreparation.max_score = agricultureActivity.landPreparation.value;
      _agricultureActivity.seedsPerArea.max_score = agricultureActivity.recommendedSeedsPerArea.value;
      _agricultureActivity.fertilizationSchedule.parent_data.parent_score =
        agricultureActivity.fertilizationSchedule.value;
      _agricultureActivity.fertilizationVolume.parent_data.parent_score = agricultureActivity.fertilizationVolume.value;
      _agricultureActivity.cropProtectionSchedule.parent_data.parent_score =
        agricultureActivity.cropProtectionSchedule.value;
      _agricultureActivity.cropProtectionVolume.parent_data.parent_score =
        agricultureActivity.cropProtectionVolume.value;
      _agricultureActivity.harvestProjection.max_score = agricultureActivity.harvestProjection.value;

      _agricultureActivity.fertilizationSchedule.parent_data.child_rules[0].max_score =
        fertilizationSchedule.firstVisit.value;
      _agricultureActivity.fertilizationSchedule.parent_data.child_rules[1].max_score =
        fertilizationSchedule.secondVisit.value;
      _agricultureActivity.fertilizationSchedule.parent_data.child_rules[2].max_score =
        fertilizationSchedule.thirdVisit.value;

      _agricultureActivity.fertilizationVolume.parent_data.child_rules[0].max_score =
        fertilizationVolume.firstVisit.value;
      _agricultureActivity.fertilizationVolume.parent_data.child_rules[1].max_score =
        fertilizationVolume.secondVisit.value;
      _agricultureActivity.fertilizationVolume.parent_data.child_rules[2].max_score =
        fertilizationVolume.thirdVisit.value;

      _agricultureActivity.cropProtectionSchedule.parent_data.child_rules[0].max_score =
        cropProtectionSchedule.firstVisit.value;
      _agricultureActivity.cropProtectionSchedule.parent_data.child_rules[1].max_score =
        cropProtectionSchedule.secondVisit.value;
      _agricultureActivity.cropProtectionSchedule.parent_data.child_rules[2].max_score =
        cropProtectionSchedule.thirdVisit.value;

      _agricultureActivity.cropProtectionVolume.parent_data.child_rules[0].max_score =
        cropProtectionVolume.firstVisit.value;
      _agricultureActivity.cropProtectionVolume.parent_data.child_rules[1].max_score =
        cropProtectionVolume.secondVisit.value;
      _agricultureActivity.cropProtectionVolume.parent_data.child_rules[2].max_score =
        cropProtectionVolume.thirdVisit.value;

      dataToSubmit.push(...Object.values(_agricultureActivity));

      // Transaction Records
      const _transactionRecords = shuruData.rules['during']['Transaction Records'].get({ noproxy: true });
      delete _transactionRecords.rule_category_score;
      _transactionRecords.marketplaceTransaction.max_score = transactionRecords.marketplace.value;
      _transactionRecords.tradingPostTransaction.max_score = transactionRecords.tradingPost.value;
      _transactionRecords.saleOfHarvestToKita.max_score = transactionRecords.sales.value;

      _transactionRecords.marketplaceTransaction.levels = _transactionRecords.marketplaceTransaction.levels.map(
        (lvl) => {
          const order = lvl['order'];
          const condition = marketplaceComputation[order].get({ noproxy: true });
          let lower_bound =
            order === 1 ? condition['from'] : marketplaceComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
          let upper_bound = order === 5 ? null : condition['upto'];

          return {
            ...lvl,
            score: condition['pts'],
            lower_bound,
            upper_bound,
          };
        },
      );
      _transactionRecords.tradingPostTransaction.levels = _transactionRecords.tradingPostTransaction.levels.map(
        (lvl) => {
          const order = lvl['order'];
          const condition = tradingPostComputation[order].get({ noproxy: true });
          let lower_bound =
            order === 1 ? condition['from'] : tradingPostComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
          let upper_bound = order === 5 ? null : condition['upto'];

          return {
            ...lvl,
            score: condition['pts'],
            lower_bound,
            upper_bound,
          };
        },
      );
      _transactionRecords.saleOfHarvestToKita.levels = _transactionRecords.saleOfHarvestToKita.levels.map((lvl) => {
        const order = lvl['order'];
        const condition = salesComputation[order].get({ noproxy: true });
        let lower_bound =
          order === 1 ? condition['from'] : salesComputation[order - 1]['upto'].get({ noproxy: true }) + 1;
        let upper_bound = order === 5 ? null : condition['upto'];

        return {
          ...lvl,
          score: condition['pts'],
          lower_bound,
          upper_bound,
        };
      });
      dataToSubmit.push(...Object.values(_transactionRecords));

      await updateRules(creditScoreGroupId, dataToSubmit);
    } catch (e) {
      catchError(e, 'onSave During');
    } finally {
      isLoadingConfirm.set(false);
      confirmDialog.set(false);
    }
  };

  useEffect(
    () => duringTotal.subscribe((v) => creditScore.total.during.set(v.agricultureActivity + v.transactionRecords)),
    [],
  );

  useEffect(
    () =>
      agricultureActivity.subscribe((v) => {
        duringTotal.agricultureActivity.set(
          v.landPreparation +
            v.recommendedSeedsPerArea +
            v.fertilizationSchedule +
            v.fertilizationVolume +
            v.cropProtectionSchedule +
            v.cropProtectionVolume +
            v.harvestProjection,
        );
      }),
    [],
  );

  useEffect(
    () =>
      fertilizationSchedule.subscribe((v) => {
        const total = v.firstVisit + v.secondVisit + v.thirdVisit;
        const isGreater = total > agricultureActivity.fertilizationSchedule.value;
        fertilizationScheduleError.set(isGreater);
      }),
    [],
  );

  useEffect(
    () =>
      fertilizationVolume.subscribe((v) => {
        const total = v.firstVisit + v.secondVisit + v.thirdVisit;
        const isGreater = total > agricultureActivity.fertilizationVolume.value;
        fertilizationVolumeError.set(isGreater);
      }),
    [],
  );

  useEffect(
    () =>
      cropProtectionSchedule.subscribe((v) => {
        const total = v.firstVisit + v.secondVisit + v.thirdVisit;
        const isGreater = total > agricultureActivity.cropProtectionSchedule.value;
        cropProtectionScheduleError.set(isGreater);
      }),
    [],
  );

  useEffect(
    () =>
      cropProtectionVolume.subscribe((v) => {
        const total = v.firstVisit + v.secondVisit + v.thirdVisit;
        const isGreater = total > agricultureActivity.cropProtectionVolume.value;
        cropProtectionVolumeError.set(isGreater);
      }),
    [],
  );

  useEffect(
    () =>
      transactionRecords.subscribe((v) => {
        duringTotal.transactionRecords.set(v.marketplace + v.tradingPost + v.sales);
      }),
    [],
  );

  return (
    <div className="grid gap-3">
      <div className="max-w-5xl">
        <Accordion type="single" collapsible className="w-full">
          {/* Agriculture Activity */}
          <AccordionItem value="item-1">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Agriculture Activity</h1>
                <div className="font-bold text-primary">{`${duringTotal.agricultureActivity.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Land Preparation</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.landPreparation.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.landPreparation.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Followed Recommended Number of Seeds per Area</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.recommendedSeedsPerArea.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.recommendedSeedsPerArea.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Fertilization Schedule</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.fertilizationSchedule.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.fertilizationSchedule.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-20 grid max-w-2xl gap-3">
                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 1st visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={fertilizationSchedule.firstVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          fertilizationSchedule.firstVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 2nd visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={fertilizationSchedule.secondVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          fertilizationSchedule.secondVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 3rd visit, assign</div>

                    <div className="relative mb-4">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={fertilizationSchedule.thirdVisit.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            fertilizationSchedule.thirdVisit.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {fertilizationScheduleError.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The total number should be ${agricultureActivity.fertilizationSchedule.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Fertilization Volume</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.fertilizationVolume.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.fertilizationVolume.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-20 grid max-w-2xl gap-3">
                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 1st visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={fertilizationVolume.firstVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          fertilizationVolume.firstVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 2nd visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={fertilizationVolume.secondVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          fertilizationVolume.secondVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 3rd visit, assign</div>

                    <div className="relative mb-4">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={fertilizationVolume.thirdVisit.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            fertilizationVolume.thirdVisit.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {fertilizationVolumeError.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The total number should be ${agricultureActivity.fertilizationVolume.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Crop Protection Schedule</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.cropProtectionSchedule.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.cropProtectionSchedule.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-20 grid max-w-2xl gap-3">
                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 1st visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={cropProtectionSchedule.firstVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          cropProtectionSchedule.firstVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 2nd visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={cropProtectionSchedule.secondVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          cropProtectionSchedule.secondVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 3rd visit, assign</div>

                    <div className="relative mb-4">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={cropProtectionSchedule.thirdVisit.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            cropProtectionSchedule.thirdVisit.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {cropProtectionScheduleError.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The total number should be ${agricultureActivity.cropProtectionSchedule.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Crop Protection Volume</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.cropProtectionVolume.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.cropProtectionVolume.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-20 grid max-w-2xl gap-3">
                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 1st visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={cropProtectionVolume.firstVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          cropProtectionVolume.firstVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 2nd visit, assign</div>

                    <div className="flex">
                      <InputSign
                        sign="pts"
                        className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                        type="number"
                        min={0}
                        placeholder="0"
                        value={cropProtectionVolume.secondVisit.value}
                        onChange={(e) => {
                          if (e.target.value.length > 3) return;
                          cropProtectionVolume.secondVisit.set(Number(e.target.value));
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-3">
                  <div className="flex items-start justify-between text-sm">
                    <div className="pt-1">If the farmer has completed the 3rd visit, assign</div>

                    <div className="relative mb-4">
                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={cropProtectionVolume.thirdVisit.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            cropProtectionVolume.thirdVisit.set(Number(e.target.value));
                          }}
                        />
                      </div>
                      {cropProtectionVolumeError.value && (
                        <div className="form-error absolute -bottom-6 w-max !text-xs">
                          {`The total number should be ${agricultureActivity.cropProtectionVolume.value} or less.`}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Harvest Projection</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={agricultureActivity.harvestProjection.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      agricultureActivity.harvestProjection.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Transaction Records */}
          <AccordionItem value="item-2">
            <AccordionTrigger showIndicator={false}>
              <div className="flex w-full max-w-5xl justify-between">
                <h1 className="font-bold text-primary">Transaction Records</h1>
                <div className="font-bold text-primary">{`${duringTotal.transactionRecords.value}%`}</div>
              </div>
            </AccordionTrigger>
            <AccordionContent className="grid gap-3 pb-8 pt-3">
              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Marketplace Transaction</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.marketplace.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.marketplace.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['1'].from.value}
                          onChange={(e) => {
                            marketplaceComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['1'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['2'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['3'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={marketplaceComputation['4'].upto.value}
                          onChange={(e) => {
                            marketplaceComputation['4'].upto.set(Number(e.target.value));
                            marketplaceComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={marketplaceComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            marketplaceComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={marketplaceComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              marketplaceComputation['5'].pts.value > transactionRecords.tradingPost.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={marketplaceComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              marketplaceComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {marketplaceComputation['5'].pts.value > transactionRecords.tradingPost.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.tradingPost.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Trading Post Transaction</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.tradingPost.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.tradingPost.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['1'].from.value}
                          onChange={(e) => {
                            tradingPostComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['1'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['2'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['3'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={tradingPostComputation['4'].upto.value}
                          onChange={(e) => {
                            tradingPostComputation['4'].upto.set(Number(e.target.value));
                            tradingPostComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={tradingPostComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            tradingPostComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={tradingPostComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              tradingPostComputation['5'].pts.value > transactionRecords.tradingPost.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={tradingPostComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              tradingPostComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {tradingPostComputation['5'].pts.value > transactionRecords.tradingPost.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.tradingPost.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex max-w-5xl items-center justify-between gap-2 pl-5 sm:pl-10">
                <h1 className="font-medium">Sales of Harvest to KITA</h1>
                <div className="flex">
                  <InputSign
                    sign="pts"
                    className={cn('max-w-[10rem] h-9 focus-visible:ring-primary')}
                    type="number"
                    min={0}
                    placeholder="0"
                    value={transactionRecords.sales.value}
                    onChange={(e) => {
                      if (e.target.value.length > 3) return;
                      transactionRecords.sales.set(Number(e.target.value));
                    }}
                  />
                </div>
              </div>

              <div className="ml-10 max-w-2xl pb-4 sm:ml-20">
                <div>
                  <div className="text-sm font-light">Points Computation Table</div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['1'].from.value}
                          onChange={(e) => {
                            salesComputation['1'].from.set(Number(e.target.value));
                          }}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['1'].upto.value}
                          onChange={(e) => {
                            salesComputation['1'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['1'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['1'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['1'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['2'].upto.value}
                          onChange={(e) => {
                            salesComputation['2'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['2'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['2'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['2'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['3'].upto.value}
                          onChange={(e) => {
                            salesComputation['3'].upto.set(Number(e.target.value));
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['3'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['3'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['3'].upto.value + 1}
                        />
                        <div>up to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary"
                          placeholder="0"
                          min={0}
                          type="number"
                          value={salesComputation['4'].upto.value}
                          onChange={(e) => {
                            salesComputation['4'].upto.set(Number(e.target.value));
                            salesComputation['5'].from.set(Number(e.target.value) + 1);
                          }}
                        />
                      </div>

                      <div className="flex">
                        <InputSign
                          sign="pts"
                          className={cn('max-w-[10rem] h-8 focus-visible:ring-primary')}
                          type="number"
                          min={0}
                          placeholder="0"
                          value={salesComputation['4'].pts.value}
                          onChange={(e) => {
                            if (e.target.value.length > 3) return;
                            salesComputation['4'].pts.set(Number(e.target.value));
                          }}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="ml-10 mt-3 grid gap-3">
                    <div className="flex flex-wrap gap-2 text-xs sm:items-center sm:justify-between sm:text-sm">
                      <div className="flex items-center gap-4">
                        <div>Greater than or equal to</div>
                        <InputSign
                          sign="PHP"
                          signPosition="left"
                          className="h-8 max-w-44 focus-visible:ring-primary disabled:bg-gray-300"
                          placeholder="0"
                          disabled
                          value={salesComputation['5'].from.value}
                        />
                      </div>

                      <div className="relative">
                        <div className="flex">
                          <InputSign
                            sign="pts"
                            className={cn(
                              'max-w-[10rem] h-9 focus-visible:ring-primary',
                              salesComputation['5'].pts.value > transactionRecords.sales.value
                                ? 'border-red-500 focus-visible:ring-red-500'
                                : '',
                            )}
                            type="number"
                            min={0}
                            placeholder="0"
                            value={salesComputation['5'].pts.value}
                            onChange={(e) => {
                              if (e.target.value.length > 3) return;
                              salesComputation['5'].pts.set(Number(e.target.value));
                            }}
                          />
                        </div>
                        {salesComputation['5'].pts.value > transactionRecords.sales.value && (
                          <div className="form-error absolute -bottom-6 w-max !text-xs">
                            {`The number you enter here should be ${transactionRecords.sales.value} or less.`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>

        <div className="flex w-full max-w-5xl justify-between py-4">
          <h1 className="font-bold text-primary">Total</h1>
          <div className="font-bold text-primary">{`${creditScore.total.during.value}%`}</div>
        </div>

        <ErrorDialog state={errorDialog} />
        <SaveConfirmation state={confirmDialog} onSave={onSave} isLoading={isLoadingConfirm.value} />
      </div>

      <div className="mt-8">
        <Button
          className="px-12"
          onClick={() => {
            if (creditScore.total.during.value > 100 || creditScore.total.during.value < 100) {
              errorDialog.set(true);
              return;
            }

            confirmDialog.set(true);
          }}
        >
          Submit
        </Button>
      </div>
    </div>
  );
}
