import React from 'react';
import { ToWords } from 'to-words';

import { FormDimensions } from '@/lib/constants/enums';

interface PersonData {
  first_name: string;
  middle_name?: string;
  last_name: string;
}

interface Page2Props {
  data: PersonData;
  farmPlan: any[];
}

const Page2: React.FC<Page2Props> = ({ data, farmPlan }) => {
  const currentDate = new Date();

  const formattedDate = currentDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  // Calculate the amount (same logic as Page1)
  const farmPlanItems = farmPlan?.[0]?.farmPlanItems || [];
  const contingency = farmPlan?.[0]?.contingency_for_fluctuation || 0;
  const farmerInput = [
    'Seed / Seedling Requirements (SE)',
    'Soil Fertilization - Basal (Top-Dress) (FE)',
    'Soil Fertilization - Additional (Side-Dress) (FE)',
    'Foliar Fertilization (Spray) (FE)',
    'Pesticide Application (Spray / Spread) (CP)',
    'Farm Materials, Consumables, etc.',
  ];

  const farmerInputTotal = farmPlanItems
    .filter((item: any) => farmerInput.includes(item?.name))
    .reduce((acc: number, item: any) => acc + (item?.total_amount || 0), 0);
  const farmInputsContingency = farmerInputTotal + (farmerInputTotal * contingency) / 100;

  const toWords = new ToWords({
    localeCode: 'en-PH',
    converterOptions: {
      currency: true,
      ignoreDecimal: false,
      ignoreZeroCurrency: false,
      doNotAddOnly: false,
      currencyOptions: {
        name: 'Peso',
        plural: 'Pesos',
        symbol: '₱',
        fractionalUnit: {
          name: 'Centavo',
          plural: 'Centavos',
          symbol: '',
        },
      },
    },
  });

  const words = toWords.convert(farmInputsContingency, { currency: true });

  return (
    <div className={`relative flex flex-col border bg-white p-20 print:border-none ${FormDimensions.LEGAL}`}>
      {/* Header */}
      <div className="mb-8 text-center">
        <h1 className="text-lg font-bold uppercase">LETTER OF AUTHORIZATION TO DEBIT FUNDS</h1>
      </div>

      {/* Date */}
      <div className="mb-6 text-sm">
        <span>Date: </span>
        <span className="inline-block w-40 border-b border-black px-2">{formattedDate}</span>
      </div>

      {/* Address Section */}
      <div className="mb-6 space-y-1 text-sm">
        <div>
          <span className="inline-block w-60 border-b border-black"></span>
        </div>
        <div>
          <span>Head, LANDBANK </span>
          <span className="inline-block w-32 border-b border-black"></span>
          <span> Lending Center</span>
        </div>
        <div>
          <span className="inline-block w-60 border-b border-black"></span>
        </div>
        <div>
          <span className="inline-block w-60 border-b border-black"></span>
        </div>
        <div>
          <span className="inline-block w-60 border-b border-black"></span>
        </div>
      </div>

      {/* ATTN Section */}
      <div className="mb-6 ml-12 space-y-2 text-sm">
        <div className="font-bold">ATTN: LANDBANK Branch Manager</div>
        <div>
          <span>Branch: </span>
          <span className="inline-block w-48 border-b border-black"></span>
        </div>
        <div>
          <span>Savings Account No: </span>
          <span className="inline-block w-56 border-b border-black"></span>
        </div>
      </div>

      {/* Salutation */}
      <div className="mb-6 text-sm">
        <span>Mr./Ms. </span>
        <span className="inline-block w-40 border-b border-black px-1">
          {/* {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim()} */}
        </span>
        <span>:</span>
      </div>

      {/* Main Content */}
      <div className="mb-6 space-y-4 text-sm">
        <div>
          <span>This is to authorize LANDBANK </span>
          <span className="font-bold underline">to debit</span>
          <span> the previously held amount of </span>
          <span className="mx-1 min-w-80 border-b border-black px-1">
            {words} (₱
            {farmInputsContingency.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })})
          </span>
          <span>
            {' '}
            in favor of KITA AGRITECH CORP as payment for the products and services provided by the said company. Such
            amount shall be debited to the following Bank Account:
          </span>
        </div>

        <div className="ml-8 space-y-2">
          <div>
            <span>Account Name: KITA AGRITECH CORP</span>
          </div>
          <div>
            <span>Account No: </span>
            <span className="inline-block w-48 border-b border-black"></span>
          </div>
          <div>
            <span>LBP Branch: </span>
            <span className="inline-block w-48 border-b border-black"></span>
          </div>
        </div>

        <div>
          <span>This authorization shall be valid </span>
          <span className="font-bold underline">thirty (30) banking days</span>
          <span> from the date stated above, or any extended period as agreed upon with KITA Agritech.</span>
        </div>

        <div>This authorization to debit shall not be valid without the following attachments:</div>

        <div className="ml-8 space-y-1">
          <div>1. Statement of Account, duly signed by myself acknowledging the total amount due</div>
          <div>2. Delivery Receipt signed by myself or my authorized representative</div>
        </div>

        <div>
          By writing my full name and signing this letter, I am fully aware that this authorization to debit the
          above-mentioned amount is part of my obligations under the Tripartite Agreement for my AGRISENSO Loan, and the
          PTMA and MSA with KITA Agritech.
        </div>

        <div>Thank you very much.</div>

        <div>Sincerely yours,</div>
      </div>

      {/* Signature Section */}
      <div className="mt-20 flex w-fit flex-col text-sm">
        <div className="mb-1 w-64 border-b border-black text-center capitalize">
          {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim()}
        </div>
        <div className="text-center text-sm">Signature over printed name</div>
      </div>
    </div>
  );
};

export default Page2;
