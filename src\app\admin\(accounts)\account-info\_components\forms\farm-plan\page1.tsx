'use client';

import { format } from 'date-fns';
import { useMemo } from 'react';

import { FormDimensions } from '@/lib/constants/enums';

interface FarmPlanSubItem {
  id: number;
  farm_plan_item_id: number;
  farm_plan_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

interface FarmPlanItem {
  id: number;
  farm_plan_id: number;
  name: string;
  slug: string;
  type: string;
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  farmPlanSubItems: FarmPlanSubItem[];
}

interface GroupedItems {
  [expectedDate: string]: {
    [category: string]: FarmPlanSubItem[];
  };
}

interface FarmerData {
  farmer: {
    first_name: string;
    last_name: string;
    address?: string;
    farmerInfo?: {
      farm_address?: string;
      farm_area?: string;
    };
  };
  farmPlans: Array<{
    reference_number: string;
    total_amount: number;
    cropping_type: string;
    agronomist_name: string;
    agronomist_prc_number: string;
    agronomist_valid_until: string;
    head_agronomist_name: string;
    head_agronomist_prc_number: string;
    head_agronomist_valid_until: string;
    crop?: {
      name: string;
    };
    farmPlanItems: FarmPlanItem[];
  }>;
}

interface PrintFarmPlanProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: FarmerData;
}

const Page1 = ({ data: farmerData }) => {
  const farmPlan = farmerData?.farmPlans?.[farmerData.farmPlans.length - 1];
  const address = farmerData?.farmer?.address ? JSON.parse(farmerData.farmer.address) : {};

  const costSummary = useMemo(() => {
    const items = farmPlan.farmPlanItems || [];
    const contingencyPercentage = farmPlan.contingency_for_fluctuation || 0;

    // Farm input item names (matching the logic from admin forms and edit form)
    const farmInputNames = [
      'Seed / Seedling Requirements (SE)',
      'Soil Fertilization - Basal (Top-Dress) (FE)',
      'Soil Fertilization - Additional (Side-Dress) (FE)',
      'Foliar Fertilization (Spray) (FE)',
      'Pesticide Application (Spray / Spread) (CP)',
      'Farm Materials, Consumables, etc.',
    ];

    // Cash requirements item names
    const cashRequirementsNames = ['Labor Requirements', 'Other Production Costs'];

    // Farmer's equity item names
    const farmersEquityNames = [
      'Non-Cash Costs',
      'KITA Subsidized Costs',
      'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
    ];

    // Calculate farm inputs total (before contingency)
    const farmInputsTotal = items
      .filter((item) => farmInputNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    const amountForHolding = farmInputsTotal + contingencyPercentage;

    // Calculate farm inputs with contingency (matching edit form logic)
    const farmInputsContingency = farmInputsTotal + (farmInputsTotal * contingencyPercentage) / 100;

    // Calculate cash requirements
    const cashRequirements = items
      .filter((item) => cashRequirementsNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    // Calculate farmer's equity
    const farmersEquity = items
      .filter((item) => farmersEquityNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    const totalProductionCosts = farmInputsContingency + cashRequirements + farmersEquity;

    return {
      farmInputsTotal,
      farmInputsContingency,
      amountForHolding,
      cashRequirements,
      farmersEquity,
      totalProductionCosts,
      farmInputsPercentage: totalProductionCosts > 0 ? (farmInputsContingency / totalProductionCosts) * 100 : 0,
      cashRequirementsPercentage: totalProductionCosts > 0 ? (cashRequirements / totalProductionCosts) * 100 : 0,
      farmersEquityPercentage: totalProductionCosts > 0 ? (farmersEquity / totalProductionCosts) * 100 : 0,
    };
  }, [farmPlan.farmPlanItems, farmPlan.contingency_for_fluctuation]);

  const formatCurrency = (value: number) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // Group farm plan sub items by expected date and category
  const groupedItems: GroupedItems = {};

  farmPlan?.farmPlanItems?.forEach((item: FarmPlanItem) => {
    if (Array.isArray(item.farmPlanSubItems)) {
      item.farmPlanSubItems.forEach((subItem: FarmPlanSubItem) => {
        const expectedDate = subItem.expected_date;
        const category = item.name;

        if (!groupedItems[expectedDate]) {
          groupedItems[expectedDate] = {};
        }
        if (!groupedItems[expectedDate][category]) {
          groupedItems[expectedDate][category] = [];
        }

        groupedItems[expectedDate][category].push(subItem);
      });
    }
  });

  // Calculate subtotals for each group
  const calculateSubtotal = (items: FarmPlanSubItem[]) => {
    return items.reduce((sum, item) => sum + item.total_amount, 0);
  };

  return (
    <div className={`relative flex ${FormDimensions.LEGAL} flex-col border p-10 font-sans print:border-none`}>
      <div>
        <div className="border bg-white py-3 text-center text-base font-bold text-black">Kita Agritech Corp</div>
        <div className="border bg-white py-1 text-center text-sm font-bold text-black">
          Farmer Kita Program - LANDBANK AGRISENSO PLUS PRODUCTION LOAN
        </div>
        <div className="border bg-white py-1 text-center text-xs font-medium text-black">
          Farm Package of Practice (PoP) Costs - INPUTS / LABOR / OTHERS / NON-CASH
        </div>
        <div className="flex flex-col text-xs">
          <div className="grid w-full grid-cols-6 border border-gray-400">
            <div className="border border-r-gray-400 bg-gray-300/80 py-1 pr-2 text-right">Farmer Name:</div>
            <div className="col-span-2 py-1 text-center ">{`${farmerData?.farmer?.first_name} ${farmerData?.farmer?.last_name}`}</div>
            <div className="border border-x-gray-400 bg-gray-300/80 py-1 pr-2 text-right">Crops:</div>
            <div className="col-span-2 py-1 text-center">{farmPlan?.crop?.name}</div>
          </div>
          <div className="grid w-full grid-cols-6 border border-gray-400">
            <div className="flex items-center justify-end border border-r-gray-400 bg-gray-300/80 py-1 pr-2 text-right">
              Home Address:
            </div>
            <div className="col-span-2 px-2 py-1 text-xs ">{`${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}</div>
            <div className="flex items-center justify-end border border-x-gray-400 bg-gray-300/80 py-1 pr-2 text-right">
              Cropping Type:
            </div>
            <div className="col-span-2 flex items-center justify-center py-1 text-center">
              {farmPlan?.cropping_type}
            </div>
          </div>
          <div className="grid w-full grid-cols-6 border border-gray-400">
            <div className="border border-r-gray-400 bg-gray-300/80 py-1 pr-2 text-right text-[11px]">
              Farm Area Size (ha):
            </div>
            <div className="col-span-2 py-1 text-center">{farmerData?.farmer?.farmerInfo?.farm_area}</div>
            <div className="border border-x-gray-400 bg-gray-300/80 py-1 pr-2 text-right">Farm Location:</div>
            <div className="col-span-2 py-1 text-center">{farmerData?.farmer?.farmerInfo?.farm_address}</div>
          </div>
        </div>
      </div>

      <div className="mb-1 mt-4 w-full text-center text-xs">To be prepared by Agronomist:</div>

      <div className="w-full bg-gray-300 py-1 text-center font-semibold">
        FARM INPUTS FOR DELIVERY / PICK-UP (Amount to be Held in favor of KITA)
      </div>

      {Object.entries(groupedItems)
        .flatMap(([expectedDate, categories]) =>
          Object.entries(categories).map(([category, items]) => ({
            expectedDate,
            category,
            items,
          })),
        )
        .slice(0, 6)
        .map(({ expectedDate, category, items }) => {
          const subtotal = calculateSubtotal(items);

          return (
            <div key={`${expectedDate}-${category}`} className="mb-3">
              <div className="border bg-white py-1 text-center text-sm font-medium text-black">{category}</div>

              <table className="w-full border-collapse border border-gray-400 text-xs">
                <thead>
                  <tr className="bg-gray-300 text-black">
                    <th className="border border-gray-400 p-2 text-left">Expected Date</th>
                    <th className="border border-gray-400 p-2 text-left">Items</th>
                    <th className="border border-gray-400 p-2 text-center">Unit</th>
                    <th className="border border-gray-400 p-2 text-center">Quantity</th>
                    <th className="border border-gray-400 p-2 text-center">Unit Cost</th>
                    <th className="border border-gray-400 p-2 text-center">Total Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((item, index) => (
                    <tr key={item.id}>
                      <td className="border border-gray-400 p-2">
                        {index === 0 ? format(new Date(expectedDate), 'MM/dd/yyyy') : ''}
                      </td>
                      <td className="border border-gray-400 p-2">{item.item_name}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.unit}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.quantity}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.unit_cost.toFixed(2)}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.total_amount.toFixed(2)}</td>
                    </tr>
                  ))}
                  <tr>
                    <td colSpan={5} className="border border-gray-400 p-2 text-right font-medium">
                      Sub Total
                    </td>
                    <td className="border border-gray-400 p-2 text-center font-medium">{subtotal.toFixed(2)}</td>
                  </tr>
                  {category === 'Farm Materials, Consumables, etc.' && (
                    <>
                      <tr>
                        <td colSpan={5} className="border border-gray-400 bg-gray-300 p-2 text-right font-medium">
                          ESTIMATED FARM INPUTS COSTS
                        </td>
                        <td className="border border-gray-400 p-2 text-center font-medium">
                          {formatCurrency(costSummary.farmInputsTotal)}
                        </td>
                      </tr>
                      <tr>
                        <td colSpan={5} className="border border-gray-400 bg-gray-300 p-2 text-right font-medium">
                          CONTINGENCY FOR FLUCTUATION
                        </td>
                        <td className="border border-gray-400 p-2 text-center font-medium">
                          {formatCurrency(farmPlan.contingency_for_fluctuation)}
                        </td>
                      </tr>
                      <tr>
                        <td
                          colSpan={5}
                          className="border border-gray-400 bg-gray-300 p-2 text-right font-medium uppercase"
                        >
                          Amount for Holdings
                        </td>
                        <td className="border border-gray-400 p-2 text-center font-medium">
                          {formatCurrency(costSummary?.farmInputsContingency)}
                        </td>
                      </tr>
                    </>
                  )}
                </tbody>
              </table>
            </div>
          );
        })}
    </div>
  );
};

export default Page1;
