'use client';

import useUserRequestsTab from '@/components/layout/admin/product-management/seeds/hooks/useUserRequestsTab';

import { useBulkImportUsers } from './hooks/useBulkImportUsers';
import { MembersTable } from './members-table';

export default function AccountRequestPage() {
  const { bulkImportUsersQuery } = useBulkImportUsers();
  const requestTab = useUserRequestsTab();

  // Only Import Users tab data
  const data = requestTab['4'];

  return (
    <div className="p-6 lg:p-8">
      <MembersTable
        columns={data.columns}
        data={bulkImportUsersQuery.data?.data || []}
        metadata={bulkImportUsersQuery.data?.meta || null}
        isLoading={bulkImportUsersQuery.isLoading}
      />
    </div>
  );
}
