'use client';

import { useHookstate } from '@hookstate/core';
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { format, isValid, parse } from 'date-fns';
import { toast } from 'sonner';

import axios, { isAxiosError } from '@/lib/api';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalState } from '@/lib/store';
import { IUserBulkUpload, IUserUploadWithStatus } from '@/lib/types/user';

// Types
interface IBulkImportUser {
  id: number;
  total_users: number;
  processed_by_id: number;
  attachment: string;
  created_at: string;
  updated_at: string;
  processedBy: {
    id: number;
    email: string;
    username: string;
    admin: {
      id: number;
      first_name: string;
      last_name: string;
    };
  };
}

interface IBulkImportUsersResponse {
  data: IBulkImportUser[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    first_page_url: string;
    last_page_url: string;
    next_page_url: string | null;
    previous_page_url: string | null;
  };
}

interface IBulkImportUsersParams {
  search: string;
  page: number;
  pageSize: number;
  sortField: string;
  sortOrder: string;
  startDate?: string;
  endDate?: string;
}

// Fetcher function
const fetchBulkImportUsers = async (params: IBulkImportUsersParams): Promise<IBulkImportUsersResponse> => {
  const queryParams: any = {
    search: params.search,
    page: params.page,
    pageSize: params.pageSize,
    sortField: params.sortField,
    sortOrder: params.sortOrder,
  };

  // Add date range if both dates are provided
  if (params.startDate && params.endDate) {
    queryParams.startDate = params.startDate;
    queryParams.endDate = params.endDate;
  }

  const { data } = await axios.get('/admin/user/farmers/imported/viewAll', {
    params: queryParams,
  });
  return data.data;
};

const formatDate = (dateStr: string) => {
  if (!dateStr) return '';

  const formatPattern = dateStr.includes('-') ? 'MM-dd-yyyy' : 'M/d/yyyy';
  const parsedDate = parse(dateStr, formatPattern, new Date());

  if (!isValid(parsedDate)) {
    console.warn('Invalid date:', dateStr);
    return '';
  }

  return format(parsedDate, 'yyyy-MM-dd');
};

// Bulk upload function
const bulkUploadUsers = async (data: IUserBulkUpload) => {
  try {
    if (!data.users.length) throw new Error('No data to be uploaded.');

    const filteredData = data.users
      .filter((user: IUserUploadWithStatus) => user?.birthDate?.trim?.() !== '' && !user.status.includes('Existing'))
      .map(({ birthDate, ...values }) => ({ ...values, birthDate: formatDate(birthDate) }));

    if (filteredData.length === 0) throw new Error('No data to be uploaded.');

    const response = await axios.post(
      '/admin/user/farmers/import',
      { ...data, users: filteredData },
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response.data;
  } catch (error) {
    throw new Error(error.message || error);
  }
};

// Hook
export const useBulkImportUsers = () => {
  const gState = useGlobalState();
  const queryClient = useQueryClient();
  const paginationState = useHookstate(gState.admin.pagination.usersBulk);

  // Debounce parameters to avoid too many API calls
  const searchDebounce = useHookstateDebounce(paginationState.search, 500);
  const startDateDebounce = useHookstateDebounce(paginationState.startDate, 500);
  const endDateDebounce = useHookstateDebounce(paginationState.endDate, 500);
  const sortFieldDebounce = useHookstateDebounce(paginationState.sortField, 500);
  const sortOrderDebounce = useHookstateDebounce(paginationState.sortOrder, 500);

  const bulkImportUsersQuery = useQuery({
    queryKey: [
      'bulkImportUsers',
      {
        page: paginationState.page.value,
        pageSize: paginationState.pageSize.value,
        search: searchDebounce.value,
        startDate: startDateDebounce.value,
        endDate: endDateDebounce.value,
        sortField: sortFieldDebounce.value,
        sortOrder: sortOrderDebounce.value,
      },
    ],
    queryFn: () =>
      fetchBulkImportUsers({
        page: paginationState.page.value,
        pageSize: paginationState.pageSize.value,
        search: searchDebounce.value,
        startDate: startDateDebounce.value,
        endDate: endDateDebounce.value,
        sortField: sortFieldDebounce.value,
        sortOrder: sortOrderDebounce.value,
      }),
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
  });

  const bulkUploadMutation = useMutation({
    mutationFn: bulkUploadUsers,
    // NOTE: onMutate runs first before bulkUploadUsers is fired, any throw err won't stop onMutate
    // onMutate: async () => {
    //   await toast.loading('Uploading users...', {
    //     description: 'Please wait...',
    //     duration: 90000,
    //   });
    // },
    onSuccess: (data: any) => {
      toast.dismiss();
      toast.success('Success', {
        description: data.message,
      });
      // Invalidate and refetch the bulk import users query
      queryClient.invalidateQueries({ queryKey: ['bulkImportUsers'] });
    },
    onError: (error: any) => {
      toast.dismiss();
      const errorMessage = error?.response?.data?.message || error.message;
      toast.error('Oops! Something went wrong', {
        description: errorMessage,
      });
    },
  });

  const bulkUploadUsersValidate = async (data: IUserBulkUpload['users'] & { status?: string }) => {
    if (!data.length) {
      return {
        success: false,
        message: 'No data to be uploaded.',
      };
    }

    try {
      const response = await axios.post('/admin/user/farmers/import/validate', {
        users: data.map(({ birthDate, ...values }) => ({
          ...values,
          birthDate: formatDate(birthDate),
        })),
      });

      return {
        success: true,
        ...response.data,
      };
    } catch (err: any) {
      if (isAxiosError(err)) {
        return {
          success: false,
          status: err.response?.status,
          ...(err.response?.data || {}),
        };
      }

      return {
        success: false,
        message: err?.message || 'Unknown error',
      };
    }
  };

  return {
    bulkImportUsersQuery,
    bulkUploadMutation,
    paginationState,
    bulkUploadUsersValidate,
  };
};

export type { IBulkImportUser, IBulkImportUsersResponse, IBulkImportUsersParams };
