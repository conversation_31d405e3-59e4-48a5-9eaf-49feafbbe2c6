import { Check } from 'lucide-react';
import React from 'react';

import { OPTIONS_SOURCE_OF_FUNDS } from '../../tab-content/account-profile/Enums';

const Page2 = ({ data }) => {
  let farm = false;
  let salaryHonoraria = false;
  let interestCommission = false;
  let otherBusinessIncome = false;
  let pension = false;
  let ofwRemittance = false;
  let otherRemittance = false;
  let other = false;
  const OTHER_SOURCEOFFUNDS = OPTIONS_SOURCE_OF_FUNDS.map(({ value }) => value);
  const sourceOfFunds = data?.source_of_funds || '';
  const hasOtherSourceOfFundsValue = sourceOfFunds?.split(',').map((val) => val.trim());

  (data?.source_of_funds || '').split(',').forEach((crop) => {
    const trimmedCrop = crop.trim();
    if (trimmedCrop === 'FARM INCOME') {
      farm = true;
    } else if (trimmedCrop === 'SALARY/HONORARIA') {
      salaryHonoraria = true;
    } else if (trimmedCrop === 'INTEREST/COMMISSION') {
      interestCommission = true;
    } else if (trimmedCrop === 'BUSINESS') {
      otherBusinessIncome = true;
    } else if (trimmedCrop === 'PENSION') {
      pension = true;
    } else if (trimmedCrop === 'OFW REMITANCE') {
      ofwRemittance = true;
    } else if (trimmedCrop === 'OTHER REMITTANCE') {
      otherRemittance = true;
    } else if (trimmedCrop === 'OTHERS') {
      other = true;
    }
  });

  let AgriInput = false;
  let LandReq = false;
  let BuyingFarm = false;
  (data?.farmerInfo?.need_farm_loan_reason || '').split(',').forEach((reason) => {
    const trimmedReason = reason.trim();
    if (trimmedReason === 'Agri-Inputs') {
      AgriInput = true;
    } else if (trimmedReason === 'Land Requirements') {
      LandReq = true;
    } else if (trimmedReason === 'Buying of Farm equipment') {
      BuyingFarm = true;
    }
  });

  let governementProg = false;
  let privateLenders = false;
  let coop = false;
  let middleMen = false;

  (data?.farmerInfo?.past_farm_loans || '').split(',').forEach((pastLoan) => {
    const trimmedLoans = pastLoan.trim();
    if (trimmedLoans === 'Government Programs') {
      governementProg = true;
    } else if (trimmedLoans === 'Private Lenders') {
      privateLenders = true;
    } else if (trimmedLoans === 'Cooperative') {
      coop = true;
    } else if (trimmedLoans === 'Middle-men') {
      middleMen = true;
    }
  });

  return (
    <div className="relative flex h-[14in] w-[8.5in] flex-col border bg-[url(/assets/forms/kita-form/kita-form-2.jpg)] bg-contain bg-top bg-no-repeat capitalize print:border-none">
      {/* Monthly Income */}
      <div className="absolute top-[6.2rem] flex">
        {/* Source of funds */}
        {/* Farm */}
        <Check strokeWidth={5} className={`absolute flex text-sm ${farm ? 'left-[14.6rem] top-[-3px]' : 'hidden'}`} />
        {/* Other Business Income */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${otherBusinessIncome ? 'left-[14.6rem] top-[1.2rem]' : 'hidden'}`}
        />
        {/* Salary Honoraria */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${salaryHonoraria ? 'left-[26.4rem] top-[-3px]' : 'hidden'}`}
        />
        {/* Interest Commission */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${interestCommission ? 'left-[26.4rem] top-[1.2rem]' : 'hidden'}`}
        />
        {/* OFW Remittance */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${ofwRemittance ? 'left-[37.1rem] top-[-3px]' : 'hidden'}`}
        />
        {/* Other Remittance */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${otherRemittance ? 'left-[37.1rem] top-[1.2rem]' : 'hidden'}`}
        />

        {/* Other */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${other ? 'left-[26.4rem] top-[2.8rem]' : 'hidden'}`}
        />

        <div className="absolute left-[31.4rem] top-[3.2rem] w-[275px] text-center text-sm">
          {hasOtherSourceOfFundsValue.filter((val) => !OTHER_SOURCEOFFUNDS.includes(val)).join(', ')}
        </div>

        <div className="absolute left-[12.6rem] top-[3.2rem]">
          {Number(Math.round(Number(data?.occupation_annual_income || 0) / 12)).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}
        </div>

        {/* farmers assosciation */}
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.is_member_of_organization === 0 ? 'left-[14.6rem] top-[7.3rem]' : 'hidden'
          }`}
        />
        <Check
          strokeWidth={5}
          className={`absolute flex text-sm ${
            data?.farmerInfo?.is_member_of_organization === 1 ? 'left-[14.6rem] top-24' : 'hidden'
          }`}
        />

        {/* Organization Name */}
        <div className="absolute left-[32.2rem] top-24 w-[250px] text-sm">{data?.farmerInfo?.organization_name}</div>

        {/* Organization Position */}
        <div className="absolute left-[32.2rem] top-[7.8rem] w-[250px] text-sm">
          {data?.farmerInfo?.organization_position}
        </div>
      </div>

      {/* Past Farm Loans */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.has_past_farm_loans === 0 ? 'left-[14.6rem] top-[18.3rem]' : 'hidden'
        }`}
      />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.has_past_farm_loans === 1 ? 'left-[14.6rem] top-[16.8rem]' : 'hidden'
        }`}
      />

      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${governementProg ? 'left-[29.3rem] top-[16.8rem]' : 'hidden'}`}
      />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${privateLenders ? 'left-[29.3rem] top-[18.1rem]' : 'hidden'}`}
      />
      <Check strokeWidth={5} className={`absolute flex text-sm ${coop ? 'left-[39.8rem] top-[16.8rem]' : 'hidden'}`} />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${middleMen ? 'left-[39.8rem] top-[18.1rem]' : 'hidden'}`}
      />

      {/* Do you need a farm loan? */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.has_need_farm_loan === 0 ? 'left-[14.6rem] top-[25.3rem]' : 'hidden'
        }`}
      />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.has_need_farm_loan === 1 ? 'left-[14.6rem] top-[23.8rem]' : 'hidden'
        }`}
      />
      {/* Agri-Inputs */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${AgriInput ? 'left-[29.3rem] top-[23.8rem]' : 'hidden'}`}
      />
      {/* Land Requirements */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${LandReq ? 'left-[29.3rem] top-[24.9rem]' : 'hidden'}`}
      />
      {/* Buying of Farm equipment */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${BuyingFarm ? 'left-[35.8rem] top-[23.8rem]' : 'hidden'}`}
      />

      <div className="absolute left-[17.2rem] top-[28.8rem] text-sm">
        {data?.farmerInfo?.purchaser_selling_location &&
          data?.farmerInfo?.purchaser_fullname &&
          data?.farmerInfo?.purchaser_selling_location + ` (${data?.farmerInfo?.purchaser_fullname})`}
      </div>

      {/* interested to sell in trading post */}
      {/* yes */}

      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.is_interested_to_sell_at_trading_post === 1 ? 'left-[32.6rem] top-[30.8rem]' : 'hidden'
        }`}
      />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerInfo?.is_interested_to_sell_at_trading_post === 0 ? 'left-[37.3rem] top-[30.8rem]' : 'hidden'
        }`}
      />

      {/* Data Privacy */}
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerDataPrivacy?.is_agree_using_data === 1 ? 'left-[3.1rem] top-[35.3rem]' : 'hidden'
        }`}
      />
      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerDataPrivacy?.is_agree_visiting_farm === 1 ? 'left-[3.1rem] top-[37.8rem]' : 'hidden'
        }`}
      />

      <Check
        strokeWidth={5}
        className={`absolute flex text-sm ${
          data?.farmerDataPrivacy?.is_agree_sharing_data === 1 ? 'left-[3.1rem] top-[40.3rem]' : 'hidden'
        }`}
      />

      {/* Signature */}
      <div className="absolute left-6 top-[46.8rem] w-[135px] text-center text-sm">
        {data?.farmerDataPrivacy?.updated_at &&
          new Date(data?.farmerDataPrivacy?.updated_at).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          })}
      </div>

      <div className="absolute left-[10.2rem] top-[46.8rem] w-[240px] text-center text-sm">
        {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim()}
      </div>

      <div className="absolute left-[10.2rem] top-[46.8rem] w-[240px] text-center text-sm">
        {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim()}
      </div>

      {data?.farmerDataPrivacy?.signature && (
        <img
          className="absolute left-[25.8rem] top-[45.5rem] h-[50px] w-[180px] object-contain"
          src={data?.farmerDataPrivacy?.signature}
          alt=""
        />
      )}
    </div>
  );
};

export default Page2;
