'use client';

import { useHookstate } from '@hookstate/core';
import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useState } from 'react';

import LoadingFull from '@/components/common/loading/loading-full';
import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function MembersTable({ columns, data, metadata = null, isLoading = false }) {
  const gState = useGlobalState();
  const usersBulkParams = useHookstate(gState.admin.pagination.usersBulk);

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    // Remove getPaginationRowModel for server-side pagination
    onSortingChange: (updater) => {
      setSorting(updater);
      // Handle server-side sorting
      if (typeof updater === 'function') {
        const newSorting = updater(sorting);
        if (newSorting.length > 0) {
          const sortField = newSorting[0].id;
          const sortOrder = newSorting[0].desc ? 'desc' : 'asc';

          // Map column IDs to backend field names
          const fieldMapping = {
            id: 'createdAt',
            total_users: 'totalUsers',
          };

          const mappedSortField = fieldMapping[sortField] || 'createdAt';

          usersBulkParams.sortField.set(mappedSortField);
          usersBulkParams.sortOrder.set(sortOrder);
        } else {
          // Reset to default sorting
          usersBulkParams.sortField.set('createdAt');
          usersBulkParams.sortOrder.set('desc');
        }
      }
    },
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    // Configure for server-side pagination and sorting
    manualPagination: true,
    manualSorting: true,
    pageCount: metadata ? metadata.last_page : -1,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      pagination: {
        pageIndex: metadata ? metadata.current_page - 1 : 0,
        pageSize: metadata ? metadata.per_page : 10,
      },
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div className="space-y-4">
      <DataTableToolbar id="bulk-import" table={table} meta={metadata} />

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {isLoading ? (
                // Loading skeleton rows
                Array.from({ length: metadata?.per_page || 10 }).map((_, index) => (
                  <TableRow key={`skeleton-${index}`}>
                    {columns.map((_, colIndex) => (
                      <TableCell key={`skeleton-cell-${colIndex}`} className="py-4">
                        {colIndex === 0 ? (
                          // Account Name column - wider skeleton
                          <Skeleton className="h-4 w-32" />
                        ) : colIndex === 1 ? (
                          // Status column - badge-like skeleton
                          <Skeleton className="h-6 w-24 rounded-full" />
                        ) : colIndex === 2 ? (
                          // Date column - medium skeleton
                          <Skeleton className="h-4 w-28" />
                        ) : colIndex === 3 ? (
                          // Email column - wider skeleton
                          <Skeleton className="h-4 w-36" />
                        ) : colIndex === 4 ? (
                          // Mobile column - medium skeleton
                          <Skeleton className="h-4 w-24" />
                        ) : (
                          // Actions column - small skeleton
                          <Skeleton className="size-8 rounded" />
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                        table.options.meta?.getRowClicked?.(row);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata ? (
        <div className={`relative ${isLoading ? 'pointer-events-none opacity-50' : ''}`}>
          <DataTablePaginationMeta
            table={table}
            meta={metadata}
            onChangePageSize={(pageSize) => {
              if (!isLoading) {
                usersBulkParams.pageSize.set(pageSize);
              }
            }}
            onChangePage={(page) => {
              if (!isLoading) {
                usersBulkParams.page.set(page);
              }
            }}
          />
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-white/50">
              <LoadingFull message="Loading..." />
            </div>
          )}
        </div>
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
