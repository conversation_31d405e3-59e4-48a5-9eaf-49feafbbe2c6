'use client';

import { get } from 'http';
import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';

import { LoanApplicationLabels, LoanApplicationStageLabels } from '@/lib/constants/enums';
import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

import { LoanApplicationActionsDialog } from './_components/loan-application-actions-dialog';

export default function LoanApplicationDetails() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();
  const { viewFarmerById } = useFarmer();

  const details = useHookstate(gStateP.selected.loanApplicationDetails);
  const data = useHookstate(gStateP.selected.account.info);
  const [address, setAddress] = useState(null);

  useEffect(() => {
    viewFarmerById(details['user_id'].value);
  }, []);

  useEffect(() => {
    if (data.value) {
      const _address = data['farmer']['address'].value ? JSON.parse(data['farmer']['address'].value) : {};
      console.log('Address: ', _address);
      setAddress(_address);
    }
  }, [data]);

  console.log('Details: ', details);
  console.log('Details: ', details['status'].get({ noproxy: true }));

  return (
    <div className="space-y-6 p-6 md:p-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="">
          <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Loan Application Details</h1>
          <Breadcrumb className="mt-2">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink onClick={() => router.back()}>Loan Applicants</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Loan Application Details</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {LoanApplicationLabels[details['status'].value]?.label === 'Pending' && (
          <div className="flex items-center justify-end gap-3">
            {details?.['loan_application_stage']?.get({ noproxy: true }) === 4 && (
              <LoanApplicationActionsDialog action="approve" />
            )}
            <LoanApplicationActionsDialog action="reject" />
          </div>
        )}
      </div>

      {/* Account Info */}
      {data.value && (
        <div className="card">
          <div className="flex gap-8">
            <div>
              {/* Profile Image */}
              <div>
                <img
                  className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                  src={
                    data['user_img'].value
                      ? urlify(data['user_img'].value, 'users/profile')
                      : '/assets/user-default.jpg'
                  }
                  alt=""
                />
              </div>

              <div className="mt-4 flex justify-center">
                <Button
                  onClick={() => {
                    router.push(`/admin/account-info/?id=${data['id'].value}`);
                  }}
                >
                  View more info
                </Button>
              </div>
            </div>

            <div className="flex-1">
              <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
              <dl className="grid grid-cols-2 gap-4">
                <div className="font-dmSans">
                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {`${data['farmer'].first_name.value} ${data['farmer'].last_name.value}`}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {`ID${data['id'].value.toString().padStart(9, '0')}`}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {`${data['farmer'].mobile_number.value ?? ''}`}
                    </dd>
                  </div>
                </div>

                <div className="font-dmSans">
                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {`${address?.addressHouseNumber ?? ''} 
              ${address?.addressBarangay ? JSON.parse(address?.addressBarangay)?.brgy_name : ''} 
              ${address?.addressCity ? JSON.parse(address?.addressCity)?.city_name : ''} 
              ${address?.addressProvince ? JSON.parse(address?.addressProvince)?.province_name : ''} 
              ${address?.addressZipCode || ''}`}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {`${new Date(data['farmer'].birth_date.value).toLocaleString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}`}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data['email'].value}`}</dd>
                  </div>
                </div>
              </dl>
            </div>
          </div>
        </div>
      )}

      {/* Loan Application Details */}
      <div className="card">
        <div className="text-xl font-bold leading-loose text-indigo-900">Loan Request Details</div>

        <dl className="grid grid-cols-2 gap-4">
          <div className="font-dmSans">
            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Submitted Loans to</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['credit_score_groups'].value}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Before Loan Score</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['before_loan_score'].value}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Loan Cycle</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['loan_cycle_number'].value}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Submission Date & Time</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['created_at'].value
                  ? format(new Date(details['created_at'].value), 'MMM dd, yyyy | hh:mm a')
                  : 'N/A'}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Loan Stage</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                <Badge
                  variant={
                    `stage${details?.['loan_application_stage']?.get({ noproxy: true })}` as
                      | 'stage1'
                      | 'stage2'
                      | 'stage3'
                      | 'stage4'
                  }
                  className="min-w-[100px] justify-center"
                >
                  {LoanApplicationStageLabels[details['loan_application_stage']?.get({ noproxy: true })]?.label}
                </Badge>
              </dd>
            </div>
          </div>

          <div className="font-dmSans">
            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Loan Approved Date</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['approved_at'].value ? format(new Date(details['approved_at'].value), 'MMM dd, yyyy') : 'N/A'}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Approved Credit Group</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['approvedCreditScoreGroup'].value ? details['approvedCreditScoreGroup']['name'].value : 'N/A'}
              </dd>
            </div>

            <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
              <dt className="text-sm font-medium leading-6 text-slate-400">Approved By</dt>
              <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                {details['approvedBy'].value
                  ? details['approvedBy']['admin'].value
                    ? `${details['approvedBy']['admin']['first_name'].value} ${details['approvedBy']['admin']['last_name'].value}`
                    : `${details['approvedBy']['finance']['first_name'].value} details['approvedBy']['finance']['last_name'].value`
                  : 'N/A'}
              </dd>
            </div>
          </div>
        </dl>
      </div>
    </div>
  );
}
