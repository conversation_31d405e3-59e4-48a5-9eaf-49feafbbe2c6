'use client';

import { useHookstate } from '@hookstate/core';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { useGlobalState } from '@/lib/store';

import { PROFILE_TAB } from '../../constants';

export function SaveConfirmation({ state, activeStep }) {
  const gState = useGlobalState();

  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);
  const step = useHookstate(activeStep);

  return (
    <Dialog
      open={dialogState.value && gState.accountProfileIsEdit.value}
      onOpenChange={(v) => {
        dialogState.set(v);
        if (!v) {
          gState.accountProfileIsEdit.set(false);
        }
      }}
    >
      <DialogContent className="font-sans sm:max-w-lg">
        <DialogHeader className="hidden">
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>

        <div className="text-center">
          <div>
            <img className="mx-auto" src="/assets/undraw/confirm.png" alt="" />
          </div>
          <div className="my-4 text-xl font-bold">Confirmation</div>
          <div>Do you confirm your intention to save the changes?</div>
        </div>

        <DialogFooter className="mt-4 sm:justify-between">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" variant="outline">
              Review Items
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button type="submit" size="lg" className="px-12" form={PROFILE_TAB[step.value].value}>
              Yes, Save
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
