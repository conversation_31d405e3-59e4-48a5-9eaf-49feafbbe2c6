'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { Edit, Plus } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { cn } from '@/lib/utils';

import { IFarmerLoanRequirementItems, LOAN_REQ_ITEMS } from '../../types/loan-requirements.types';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

// Mobile Card Component for individual requirement items
function MobileRequirementCard({ item }: { item: IFarmerLoanRequirementItems }) {
  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Status and Requirement Name */}
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1">
              <h3 className="text-sm font-medium leading-tight text-gray-900">{LOAN_REQ_ITEMS[item.name]}</h3>
            </div>
            <Badge
              className={cn('shrink-0', {
                'bg-green-500 hover:bg-green-600': item.is_completed === 1,
                'bg-orange-500 hover:bg-orange-600': item.is_completed === 0,
              })}
            >
              {item.is_completed === 1 ? 'Done' : 'Pending'}
            </Badge>
          </div>

          {/* Attachments */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Attachments:</span>
            <div>
              {item.attachment ? (
                <Button variant="link" className="h-auto p-0 text-blue-600" asChild>
                  <Link href={item.attachment} target="_blank">
                    View File
                  </Link>
                </Button>
              ) : (
                <span className="text-sm text-muted-foreground">-</span>
              )}
            </div>
          </div>

          {/* Notes */}
          {item.notes && (
            <div className="space-y-1">
              <span className="text-sm text-gray-600">Notes:</span>
              <p className="text-sm text-gray-900">{item.notes}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between border-t pt-2">
            <div className="flex gap-2">
              {item.is_completed === 0 ? (
                <Button size="sm" className="h-8 px-3">
                  <Plus className="mr-1 size-3" />
                  Add
                </Button>
              ) : (
                <Button size="sm" variant="outline" className="h-8 px-3">
                  <Edit className="mr-1 size-3" />
                  Edit
                </Button>
              )}
            </div>
            <Button variant="link" className="h-auto p-0 text-blue-600" asChild>
              <Link href={`/admin/audit-logs?id=${item.id}&type=loan-requirement`}>View Logs</Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function LoanRequirementDetailsTable({ columns, data }) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);
      },
    },
  });

  return (
    <div className="space-y-4">
      {/* Mobile View - Cards */}
      <div className="block md:hidden">
        {data?.length ? (
          data.map((item: IFarmerLoanRequirementItems) => <MobileRequirementCard key={item.id} item={item} />)
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">No results.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Desktop View - Table */}
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <HorizontalScrollBar>
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map((header) => {
                      return (
                        <TableHead key={header.id}>
                          {header.isPlaceholder
                            ? null
                            : flexRender(header.column.columnDef.header, header.getContext())}
                        </TableHead>
                      );
                    })}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      className="hover:cursor-pointer"
                      key={row.id}
                      data-state={row.getIsSelected() && 'selected'}
                      onClick={(event) => {
                        if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                          table.options.meta?.getRowClicked?.(row);
                      }}
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={columns.length} className="h-24 text-center">
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </HorizontalScrollBar>
        </div>
      </div>

      {/* Pagination */}
      <DataTablePagination table={table} />
    </div>
  );
}
