'use client';

import { useHookstate } from '@hookstate/core';
import { ChevronLeftIcon, ChevronRightIcon, DoubleArrowLeftIcon, DoubleArrowRightIcon } from '@radix-ui/react-icons';
import { format, parseISO } from 'date-fns';
import { UploadCloud } from 'lucide-react';
import { useEffect } from 'react';
import { BsFillTrashFill } from 'react-icons/bs';
import { CSVReader } from 'react-papaparse';
import { toast } from 'sonner';

import { DataTableFacetedFilter } from '@/components/layout/table/normal/table-faceted-filter';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalState } from '@/lib/store';
import { IUserUpload, IUserUploadWithStatus } from '@/lib/types/user';
import { cn } from '@/lib/utils';

import { useBulkImportUsers } from '../../hooks/useBulkImportUsers';

export default function UserCsv() {
  const gState = useGlobalState();
  const { bulkUploadUsersValidate } = useBulkImportUsers();
  const filteredData = useHookstate([]);
  const sData = useHookstate([]);
  const sDataValues = sData.get({ noproxy: true });
  const sDataPaginated = useHookstate({
    data: [],
  });

  const page = useHookstate(1);
  const pageSize = useHookstate(10);

  const reset = () => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const handleOnDrop = async (data, file) => {
    const parsedData = uploadHandler(data);

    const transformedData = parsedData.map((row) => {
      const newRow = { ...row };
      if (newRow['5']) {
        newRow['5'] = newRow['5'].toUpperCase();
      }
      return newRow;
    });
    console.log('transformedData', transformedData);

    const savedData = transformedData.map((x) => ({
      status: x.status,
      lastName: x[0],
      firstName: x[1],
      middleName: x[2],
      birthDate: x[3],
      placeOfBirth: x[4],
      gender: x[5],
      rsbsaNumber: x[6],
      farmArea: x[7],
      mobileNumber: x[8],
      nationality: 'Filipino',
    }));

    const res = await bulkUploadUsersValidate(savedData);

    const updatedSavedData = savedData.map((saved) => {
      const hasMatch = res.data?.some((item) => {
        return (
          item.firstName === saved.firstName &&
          item.lastName === saved.lastName &&
          item.birthDate &&
          format(parseISO(item.birthDate), 'MM-dd-yyyy') === saved.birthDate &&
          String(item.farmArea) === String(saved.farmArea) &&
          item.mobileNumber === saved.mobileNumber &&
          item.placeOfBirth === saved.placeOfBirth &&
          item.gender === saved.gender &&
          item.nationality === saved.nationality &&
          item.rsbsaNumber === saved.rsbsaNumber
        );
      });

      let status = saved.status;

      if (hasMatch) {
        // Remove "Valid" if present
        status = status
          .split(',')
          .map((s) => s.trim())
          .filter((s) => s.toLowerCase() !== 'valid');

        // Add "Existing" if not already in the list
        if (!status.some((s) => s.toLowerCase() === 'existing')) {
          status.push('Existing');
        }

        status = status.join(', ');
      }

      return {
        ...saved,
        status,
      };
    });

    sData.set(updatedSavedData);
    filteredData.set(updatedSavedData);
    gState.admin.usersUpload.set(updatedSavedData);
    gState.admin.usersUploadFile.set(file);
  };

  console.log('sData', sDataValues);

  const handleOnError = (err) => {
    toast.error('Oops!', {
      description: err.message,
    });
  };

  const handleOnRemoveFile = () => {
    sData.set([]);
    filteredData.set([]);
    sDataPaginated.set({
      data: [],
    });
  };

  const uploadHandler = (data) => {
    const tmp = [];

    if (data.length === 0) {
      toast.error('Oops!', { description: `No data found!` });
      return tmp;
    }

    data = data.filter((v) => v.errors.length === 0);

    const headerRow = data[0].data;
    const requiredHeaders = [
      'Surname',
      'Given Name',
      'Middle Name',
      'Birthdate (MM-DD-YYYY)',
      'Birthplace',
      'Sex (at Birth) MALE/FEMALE',
      'RSBSA Number',
      'Farm Area (# of hecterage)',
      'Mobile Number',
    ];

    const hasAllHeaders = requiredHeaders.every((h) => headerRow.includes(h));
    if (!hasAllHeaders) {
      toast.error('Oops!', { description: `Invalid Data` });
      return tmp;
    }

    const rawRows = [];

    data.forEach((el, index) => {
      if (index === 0) return;
      const isRowEmpty = el.data.every((cell) => cell.trim() === '');
      if (!isRowEmpty) {
        rawRows.push(el.data);
      }
    });

    const nameMap = new Map();
    rawRows.forEach((row) => {
      const key = `${row[0]?.trim().toLowerCase()}|${row[1]?.trim().toLowerCase()}`;
      nameMap.set(key, (nameMap.get(key) || 0) + 1);
    });

    const processed = rawRows.map((row) => {
      const key = `${row[0]?.trim().toLowerCase()}|${row[1]?.trim().toLowerCase()}`;
      const statuses = [];

      if (!row[3]?.trim()) statuses.push('Birthdate');
      if (!row[6]?.trim()) statuses.push('RSBSA Number');
      if (nameMap.get(key) > 1) statuses.push('Duplicated');

      return {
        ...row,
        status: statuses.join(', ') || 'Valid',
      };
    });

    return processed;
  };

  const handleDeleteRow = (indexToDelete: number) => {
    const updatedData = sDataValues.filter((_, i) => i !== indexToDelete);

    // === REBUILD DUPLICATE STATUS AFTER DELETION ===
    const nameMap = new Map<string, number>();
    updatedData.forEach((row) => {
      const key = `${row.firstName?.trim().toLowerCase()}|${row.lastName?.trim().toLowerCase()}`;
      nameMap.set(key, (nameMap.get(key) || 0) + 1);
    });

    const recalculatedData = updatedData.map((row) => {
      let statuses = row.status ? row.status.split(',').map((s) => s.trim()) : [];

      // Remove "Duplicated" if no longer duplicate
      const key = `${row.firstName?.trim().toLowerCase()}|${row.lastName?.trim().toLowerCase()}`;
      if (nameMap.get(key) > 1) {
        if (!statuses.includes('Duplicated')) statuses.push('Duplicated');
      } else {
        statuses = statuses.filter((s) => s !== 'Duplicated');
      }

      return {
        ...row,
        status: statuses.length > 0 ? statuses.join(', ') : 'Valid',
      };
    });

    // Update stores
    sData.set(recalculatedData);
    filteredData.set(recalculatedData);
    sDataPaginated.set({
      data: recalculatedData,
    });
  };

  const updateSDataCell = (rowIndex: number, columnIndex: string, value: any) => {
    const updated = [...sDataValues];
    const absIndex = getAbsoluteIndex(rowIndex);

    // Update the specific cell
    updated[absIndex][columnIndex] = value;

    // === DUPLICATE CHECK SUPPORT ===
    const buildDuplicateKey = (row: any) => {
      const firstName = row.firstName?.trim().toLowerCase() || '';
      const lastName = row.lastName?.trim().toLowerCase() || '';
      const middleName = row.middleName?.trim().toLowerCase() || '';

      if (row.birthDate?.trim()) {
        return `NAME_DOB|${firstName}|${lastName}|${row.birthDate.trim()}`;
      }
      if (!row.birthDate?.trim() && row.rsbsaNumber?.trim()) {
        return `RSBSA|${row.rsbsaNumber.trim()}`;
      }
      // No RSBSA but has names
      return `NAME_ONLY|${firstName}|${lastName}|${middleName}`;
    };

    // Build a map of duplicate keys
    const duplicateMap = new Map<string, number>();
    updated.forEach((r) => {
      const key = buildDuplicateKey(r);
      if (key) {
        duplicateMap.set(key, (duplicateMap.get(key) || 0) + 1);
      }
    });

    // Recalculate status for this row
    const row = updated[absIndex];
    let statuses = row.status ? row.status.split(',').map((s) => s.trim()) : [];

    if (['birthDate', 'rsbsaNumber', 'firstName', 'lastName', 'middleName'].includes(columnIndex)) {
      // Birthdate validation
      if (row.birthDate?.trim()) {
        statuses = statuses.filter((s) => s !== 'Birthdate');
      } else if (!statuses.includes('Birthdate')) {
        statuses.push('Birthdate');
      }

      // RSBSA Number validation
      if (row.rsbsaNumber?.trim()) {
        statuses = statuses.filter((s) => s !== 'RSBSA Number');
      } else if (!statuses.includes('RSBSA Number')) {
        statuses.push('RSBSA Number');
      }

      // Duplicate validation
      const key = buildDuplicateKey(row);
      if (key && duplicateMap.get(key) > 1) {
        if (!statuses.includes('Duplicated')) statuses.push('Duplicated');
      } else {
        statuses = statuses.filter((s) => s !== 'Duplicated');
      }
    }

    // Clean up status
    row.status = statuses.length > 0 ? statuses.join(', ') : 'Valid';

    updated[absIndex] = row;
    sData.set(updated);
  };

  const paginator = (items, current_page, per_page_items) => {
    let page = current_page || 1,
      per_page = per_page_items || 10,
      offset = (page - 1) * per_page,
      paginatedItems = items.slice(offset).slice(0, per_page_items),
      total_pages = Math.ceil(items.length / per_page);

    return {
      page: page,
      per_page: per_page,
      pre_page: page - 1 ? page - 1 : null,
      next_page: total_pages > page ? page + 1 : null,
      total: items.length,
      total_pages: total_pages,
      data: paginatedItems,
    };
  };

  const getAbsoluteIndex = (indexOnPage: number) => {
    return (page.value - 1) * pageSize.value + indexOnPage;
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const search = e.target.value.toLowerCase().trim();

    if (!search) {
      filteredData.set(sDataValues);
      return;
    }

    const filtered = sDataValues.filter((row: IUserUploadWithStatus) =>
      [row.status, row.firstName, row.lastName, row.rsbsaNumber].some((cell) =>
        String(cell).toLowerCase().includes(search),
      ),
    );

    filteredData.set(filtered);
    page.set(1);
  };

  useEffect(() => {
    sDataPaginated.set(paginator(filteredData.get({ noproxy: true }), page.value, pageSize.value));
  }, [page, pageSize, filteredData]);

  useEffect(() => {
    reset();
  }, []);

  return (
    <div className="space-y-6 px-6">
      <DataTableFacetedFilter
        data={sData}
        filteredData={filteredData}
        column="status"
        title="Status"
        options={[
          { label: 'Duplicated', value: 'Duplicated' },
          { label: 'No Birthdate', value: 'Birthdate' },
          { label: 'No RSBSA', value: 'RSBSA Number' },
          { label: 'Valid', value: 'Valid' },
          { label: 'Existing', value: 'Existing' },
        ]}
      />

      <Input onChange={handleSearch} placeholder="Search name/RSBSA number ..." />
      <div className="">
        <div className={cn(sData.length > 0 && 'hidden')}>
          <CSVReader
            config={{ header: false }}
            onDrop={handleOnDrop}
            onError={handleOnError}
            addRemoveButton
            removeButtonColor="#ef4444"
            onRemoveFile={handleOnRemoveFile}
            style={{
              dropFile: {
                width: 150,
                height: 120,
              },
              fileNameInfo: {
                color: '#4b5563',
              },
              fileSizeInfo: {
                color: '#6366f1',
              },
            }}
          >
            <UploadCloud className="size-12" />
            <div className="mt-2 text-gray-500">Drag & drop file here</div>
            <div className="text-gray-500">Or</div>
            <div className="font-bold text-primary">Browser</div>
          </CSVReader>
        </div>

        {sDataValues.length === 0 && (
          <div className="mt-3 text-center text-sm text-gray-500">
            Only CSV files are supported
            <div className="">
              Use the template provided here:
              <a
                className="ml-1 italic text-blue-500 hover:underline hover:underline-offset-2"
                href="/template/Bulk_User.csv"
                target="_blank"
              >
                Template
              </a>
            </div>
          </div>
        )}

        {sData.length > 0 && (
          <div className="grid">
            <ScrollArea className="max-h-[36vh] whitespace-nowrap rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead></TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Surname</TableHead>
                    <TableHead>Given Name</TableHead>
                    <TableHead>Middle Name</TableHead>
                    <TableHead>Birthdate</TableHead>
                    <TableHead>Birthplace</TableHead>
                    <TableHead>Sex (at Birth)</TableHead>
                    <TableHead>RSBSA Number</TableHead>
                    <TableHead>Farm Area (# of hecterage)</TableHead>
                    <TableHead>Mobile Number</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sDataPaginated['data'].value.map((col: IUserUploadWithStatus, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => handleDeleteRow(getAbsoluteIndex(index))}
                        >
                          <BsFillTrashFill className="size-4" />
                        </Button>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {col.status?.split(',').map((status, idx) => (
                            <Badge key={idx} variant={status === 'Valid' ? 'success' : 'error'}>
                              {status.trim()}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.lastName}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'lastName', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.firstName}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'firstName', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.middleName}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'middleName', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="date"
                          value={
                            col.birthDate
                              ? (() => {
                                  // Try to parse birthDate and format as yyyy-MM-dd
                                  const d = new Date(col.birthDate);
                                  if (!isNaN(d.getTime())) {
                                    // Pad month and day
                                    const yyyy = d.getFullYear();
                                    const mm = String(d.getMonth() + 1).padStart(2, '0');
                                    const dd = String(d.getDate()).padStart(2, '0');
                                    return `${yyyy}-${mm}-${dd}`;
                                  }
                                  return '';
                                })()
                              : ''
                          }
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'birthDate', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.placeOfBirth}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'placeOfBirth', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <Select
                          defaultValue="members"
                          value={col.gender}
                          onValueChange={(v) => updateSDataCell(index, 'gender', v)}
                        >
                          <SelectTrigger className="w-[180px]" icon="up-down">
                            <SelectValue placeholder="Members" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="MALE">MALE</SelectItem>
                              <SelectItem value="FEMALE">FEMALE</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.rsbsaNumber}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'rsbsaNumber', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.farmArea}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'farmArea', e.target.value)}
                        />
                      </TableCell>
                      <TableCell>
                        <input
                          type="text"
                          value={col.mobileNumber}
                          className="w-[150px] border px-2 py-1 text-sm"
                          onChange={(e) => updateSDataCell(index, 'mobileNumber', e.target.value)}
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <ScrollBar orientation="horizontal" />
            </ScrollArea>

            {/* pagination */}
            <div className="mt-4 space-y-2 px-2">
              <div className="flex items-center justify-center gap-4">
                <div className="text-sm text-gray-500">Total of {filteredData.length} row(s)</div>

                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium">Rows per page</p>
                  <Select
                    value={`${pageSize.value}`}
                    onValueChange={(value) => {
                      pageSize.set(Number(value));
                    }}
                  >
                    <SelectTrigger className="h-8 w-[70px]">
                      <SelectValue placeholder={pageSize.value} />
                    </SelectTrigger>
                    <SelectContent side="top">
                      {[10, 20, 30, 40, 50].map((pageS) => (
                        <SelectItem key={pageS} value={`${pageS}`}>
                          {pageS}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex flex-col items-center gap-2">
                <div className="flex items-center gap-2">
                  <div className="flex w-[100px] items-center justify-center text-sm font-medium">
                    Page {page.value} of {sDataPaginated['total_pages'].value}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => page.set(1)}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to first page</span>
                      <DoubleArrowLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value > 1) {
                          page.set((v) => v - 1);
                        }
                      }}
                      disabled={page.value == 1}
                    >
                      <span className="sr-only">Go to previous page</span>
                      <ChevronLeftIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="size-8 p-0"
                      onClick={() => {
                        if (page.value < sDataPaginated['total_pages'].value) {
                          page.set((v) => v + 1);
                        }
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to next page</span>
                      <ChevronRightIcon className="size-4" />
                    </Button>
                    <Button
                      variant="outline"
                      className="hidden size-8 p-0 lg:flex"
                      onClick={() => {
                        page.set(sDataPaginated['total_pages'].get({ noproxy: true }));
                      }}
                      disabled={sDataPaginated.data.length > 0 && !sDataPaginated['next_page'].value}
                    >
                      <span className="sr-only">Go to last page</span>
                      <DoubleArrowRightIcon className="size-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
