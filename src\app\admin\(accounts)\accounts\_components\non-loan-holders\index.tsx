'use client';

import { useEffect } from 'react';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useMember from '@/lib/hooks/admin/useMember';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';

import { NonLoanHolderTable } from './non-loan-holder-table';
import { NonLoanHolderColumns } from './non-loan-holder-table/columns';

export default function NonLoanHoldersPage() {
  const { getNonLoanHolders, nonLoanHolders, pagination, getNonLoanHoldersDashboard } = useMember();
  const { getGroups } = useCreditScoreMgt();

  const pageSizeDebounce = useHookstateDebounce(pagination.pageSize, 500);
  const pageDebounce = useHookstateDebounce(pagination.page, 500);
  const creditScoreGroupIdDebounce = useHookstateDebounce(pagination.creditScoreGroupId, 500);
  const lastTransactionDaysDebounce = useHookstateDebounce(pagination.lastTransactionDays, 500);
  const ratingsDebounce = useHookstateDebounce(pagination.ratings, 500);

  const trpStartDebounce = useHookstateDebounce(pagination.totalTradingpostTransactionStart, 1000);
  const trpEndDebounce = useHookstateDebounce(pagination.totalTradingpostTransactionEnd, 1000);
  const marketplaceStartDebounce = useHookstateDebounce(pagination.totalMarketplaceTransactionStart, 1000);
  const marketplaceEndDebounce = useHookstateDebounce(pagination.totalMarketplaceTransactionEnd, 1000);
  const salesStartDebounce = useHookstateDebounce(pagination.totalSalesTransactionStart, 1000);
  const salesEndDebounce = useHookstateDebounce(pagination.totalSalesTransactionEnd, 1000);

  useEffect(() => {
    getGroups();
  }, []);

  useEffect(() => {
    Promise.all([getNonLoanHolders(), getNonLoanHoldersDashboard()]);
  }, [
    pageSizeDebounce,
    pageDebounce,
    creditScoreGroupIdDebounce,
    lastTransactionDaysDebounce,
    trpStartDebounce,
    trpEndDebounce,
    marketplaceStartDebounce,
    marketplaceEndDebounce,
    salesStartDebounce,
    salesEndDebounce,
    ratingsDebounce,
  ]);

  return (
    <div className="p-6 lg:p-8">
      <NonLoanHolderTable
        columns={NonLoanHolderColumns}
        data={nonLoanHolders.data.get({ noproxy: true })}
        metadata={nonLoanHolders['meta'].get({ noproxy: true })}
      />
    </div>
  );
}
