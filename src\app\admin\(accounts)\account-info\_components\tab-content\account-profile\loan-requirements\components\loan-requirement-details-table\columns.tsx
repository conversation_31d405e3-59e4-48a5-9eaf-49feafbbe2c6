'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Edit, Plus } from 'lucide-react';
import Link from 'next/link';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { cn } from '@/lib/utils';

import { IFarmerLoanRequirementItems, LOAN_REQ_ITEMS } from '../../types/loan-requirements.types';

export const columnLoanRequirementDetails: ColumnDef<IFarmerLoanRequirementItems>[] = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <Badge
          className={cn('min-w-max', {
            'bg-green-500': data.is_completed === 1,
            'bg-orange-500': data.is_completed === 0,
          })}
        >
          {data.is_completed === 1 ? 'Done' : 'Pending'}
        </Badge>
      );
    },
    accessorFn: (row) => {
      return row.is_completed === 1 ? 'Done' : 'Pending';
    },
  },
  {
    id: 'requirements_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Requirements Name" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{LOAN_REQ_ITEMS[data.name]}</div>;
    },
    accessorFn: (row) => {
      return LOAN_REQ_ITEMS[row.name];
    },
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="flex items-center gap-2">
          {data.is_completed === 0 ? (
            <Button size="sm" className="h-8 px-3">
              <Plus className="mr-1 size-3" />
              Add
            </Button>
          ) : (
            <Button size="sm" variant="outline" className="h-8 px-3">
              <Edit className="mr-1 size-3" />
              Edit
            </Button>
          )}
        </div>
      );
    },
  },
  {
    id: 'attachments',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Attachments" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.attachment ? (
            <Button variant="link" className="h-auto p-0 text-blue-600" asChild>
              <Link href={data.attachment} target="_blank">
                View File
              </Link>
            </Button>
          ) : (
            <span className="text-muted-foreground">-</span>
          )}
        </div>
      );
    },
  },
  {
    id: 'notes',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Notes" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max max-w-xs">
          {data.notes ? (
            <span className="text-sm">{data.notes}</span>
          ) : (
            <span className="text-muted-foreground">-</span>
          )}
        </div>
      );
    },
  },
  {
    id: 'audit_logs',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Audit logs" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          <Button variant="link" className="h-auto p-0 text-blue-600" asChild>
            <Link href={`/admin/audit-logs?id=${data.id}&type=loan-requirement`}>View Logs</Link>
          </Button>
        </div>
      );
    },
  },
];
