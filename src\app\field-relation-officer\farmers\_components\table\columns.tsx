'use client';

import { format } from 'date-fns';
import { EyeIcon } from 'lucide-react';
import Link from 'next/link';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Button } from '@/components/ui/button';

import { paths } from '@/features/field-relation-officer/layout/constants';

export const columns = [
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    accessorFn: (row) => {
      const farmer = row.farmerUser.farmer;
      return `${farmer.first_name || ''} ${farmer.last_name || ''}`;
    },
  },
  {
    id: 'email',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
    accessorFn: (row) => row.farmerUser.email,
  },
  {
    id: 'created_at',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date Registered" />,
    accessorFn: (row) => format(new Date(row.created_at), 'dd MMM yyyy | hh:mm a'),
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Actions" className="text-center" />,
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data = row.original;

  return (
    <div className="flex justify-center gap-2">
      <Button asChild className="h-8 px-2 lg:px-3" variant="outline" size="sm">
        <Link href={paths.dashboard.farmers.view(data.farmer_user_id)} className="flex items-center gap-2">
          <EyeIcon className="size-4" /> View Profile
        </Link>
      </Button>
    </div>
  );
};
