'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import useCrops, { CropPriceRangeSchema } from '@/lib/hooks/useCrops';
import useDebounce from '@/lib/hooks/utils/useDebounce';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export default function AddNewPrice() {
  const gState = useGlobalState();
  const { getCrops, updatePriceRange } = useCrops();
  const search = useHookstate('');
  const { debouncedValue: debouncedSearch } = useDebounce(search.value, 500);

  const { register, handleSubmit, control, trigger, formState } = useForm({
    resolver: zodResolver(CropPriceRangeSchema),
    defaultValues: {
      cropsPrices: [
        {
          cropId: 0,
          lowPrice: 0,
          highPrice: 0,
          lowBaptcPrice: 0,
          highBaptcPrice: 0,
          lowNvatPrice: 0,
          highNvatPrice: 0,
        },
      ],
    },
  });
  const { fields, replace } = useFieldArray({
    name: 'cropsPrices',
    control,
  });

  const onSubmit = async (data) => {
    /* data.cropsPrices = data.cropsPrices.filter(
      (crop) =>
        (crop.lowPrice > 0 && crop.highPrice > 0) ||
        (crop.lowBaptcPrice > 0 && crop.highBaptcPrice > 0) ||
        (crop.lowNvatPrice > 0 && crop.highNvatPrice > 0),
    ); */

    // Get original crops data for reference
    const cropsData = gState.admin.crops.data.get({ noproxy: true });

    // Correct price ranges with two-step logic
    const correctedData = {
      ...data,
      cropsPrices: data.cropsPrices.map((crop) => {
        // Find original crop data
        const originalCrop = cropsData.find((c) => c.id === crop.cropId);
        const hasOriginalPrice = originalCrop?.cropPriceRanges?.length > 0;

        // Get original values
        const originalLowPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_price : 0;
        const originalHighPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_price : 0;
        const originalLowBaptcPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_baptc_price : 0;
        const originalHighBaptcPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_baptc_price : 0;
        const originalLowNvatPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].low_nvat_price : 0;
        const originalHighNvatPrice = hasOriginalPrice ? originalCrop.cropPriceRanges[0].high_nvat_price : 0;

        // Step 1: Replace 0 in low price with original only when high price equals original high price
        let correctedLowPrice =
          crop.lowPrice === 0 && crop.highPrice === originalHighPrice && crop.highPrice !== 0
            ? originalLowPrice
            : crop.lowPrice;
        let correctedHighPrice = crop.highPrice;
        let correctedLowBaptcPrice =
          crop.lowBaptcPrice === 0 && crop.highBaptcPrice === originalHighBaptcPrice && crop.highBaptcPrice !== 0
            ? originalLowBaptcPrice
            : crop.lowBaptcPrice;
        let correctedHighBaptcPrice = crop.highBaptcPrice;
        let correctedLowNvatPrice =
          crop.lowNvatPrice === 0 && crop.highNvatPrice === originalHighNvatPrice && crop.highNvatPrice !== 0
            ? originalLowNvatPrice
            : crop.lowNvatPrice;
        let correctedHighNvatPrice = crop.highNvatPrice;

        // Step 2: Fix invalid ranges where low > high
        // Trading Post Price
        if (correctedLowPrice > correctedHighPrice && correctedHighPrice > 0) {
          correctedLowPrice = correctedHighPrice;
          correctedHighPrice = correctedHighPrice; // Both values become the valid bound (high price)
        }

        // BAPTC Price
        if (correctedLowBaptcPrice > correctedHighBaptcPrice && correctedHighBaptcPrice > 0) {
          correctedLowBaptcPrice = correctedHighBaptcPrice;
          correctedHighBaptcPrice = correctedHighBaptcPrice; // Both values become the valid bound (high price)
        }

        // NVAT Price
        if (correctedLowNvatPrice > correctedHighNvatPrice && correctedHighNvatPrice > 0) {
          correctedLowNvatPrice = correctedHighNvatPrice;
          correctedHighNvatPrice = correctedHighNvatPrice; // Both values become the valid bound (high price)
        }

        return {
          ...crop,
          lowPrice: correctedLowPrice,
          highPrice: correctedHighPrice,
          lowBaptcPrice: correctedLowBaptcPrice,
          highBaptcPrice: correctedHighBaptcPrice,
          lowNvatPrice: correctedLowNvatPrice,
          highNvatPrice: correctedHighNvatPrice,
        };
      }),
    };

    console.log('onSubmit original: ', data);
    console.log('onSubmit corrected: ', correctedData);
    updatePriceRange(correctedData);
  };

  // Initial fetch
  useEffect(() => {
    getCrops();
  }, []);

  useEffect(() => {
    const temp = [];

    // Fetch crops data without using proxy
    let cropsData = gState.admin.crops.data.get({ noproxy: true }).filter((crop) => crop.status === 1);

    // search filter
    cropsData = cropsData.filter((crop) => crop.name.toLowerCase().includes(debouncedSearch.toLowerCase()));

    // Iterate through the crops data and append new items to the list
    cropsData.forEach((crop) => {
      const hasPrice = crop.cropPriceRanges.length > 0;
      temp.push({
        cropId: crop.id,
        lowPrice: hasPrice ? crop.cropPriceRanges[0].low_price : 0,
        highPrice: hasPrice ? crop.cropPriceRanges[0].high_price : 0,
        lowBaptcPrice: hasPrice ? crop.cropPriceRanges[0].low_baptc_price : 0,
        highBaptcPrice: hasPrice ? crop.cropPriceRanges[0].high_baptc_price : 0,
        lowNvatPrice: hasPrice ? crop.cropPriceRanges[0].low_nvat_price : 0,
        highNvatPrice: hasPrice ? crop.cropPriceRanges[0].high_nvat_price : 0,
      });
    });

    replace(temp);
  }, [gState.admin.crops.data, debouncedSearch]);

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col justify-between gap-4 md:flex-row">
        <div className="">
          <h1 className="text-2xl font-bold text-primary">Add New Price</h1>
          <p className="text-gray-500">{`Add new price for the following crops`}</p>
        </div>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button className="px-12" type="button">
              Submit
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently add new price to all crops listed here.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
              <AlertDialogAction type="submit" form="add-new-price">
                Continue
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>

      <div>
        <Input
          placeholder="Search..."
          value={search.value}
          onChange={(event) => search.set(event.target.value)}
          className="h-8 w-[150px] lg:w-[250px]"
        />
      </div>

      <form id="add-new-price" onSubmit={handleSubmit(onSubmit)}>
        <ScrollArea className="h-[75vh]">
          <Table className="">
            <TableHeader>
              <TableRow>
                <TableHead className="text-sm font-bold text-gray-600">Crop Name</TableHead>
                <TableHead className="text-sm font-bold text-gray-600">Trading Post Price</TableHead>
                <TableHead className="text-sm font-bold text-gray-600">BAPTC Price per kilo</TableHead>
                <TableHead className="text-sm font-bold text-gray-600">NVAT Price per kilo</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody className="">
              {/* filter by cropName using debouncedSearch */}
              {fields.map((field, index) => {
                const cropId = formState.errors?.cropsPrices?.[index]?.cropId;
                const lowPrice = formState.errors?.cropsPrices?.[index]?.lowPrice;
                const highPrice = formState.errors?.cropsPrices?.[index]?.highPrice;
                const lowBaptcPrice = formState.errors?.cropsPrices?.[index]?.lowBaptcPrice;
                const highBaptcPrice = formState.errors?.cropsPrices?.[index]?.highBaptcPrice;
                const lowNvatPrice = formState.errors?.cropsPrices?.[index]?.lowNvatPrice;
                const highNvatPrice = formState.errors?.cropsPrices?.[index]?.highNvatPrice;

                const cropData = gState.admin.crops.data
                  .get({ noproxy: true })
                  .find((crop) => crop.id === field.cropId);
                const cropName = cropData?.name;
                const cropImage = cropData?.image;

                return (
                  <TableRow key={field.id}>
                    <TableCell>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <img
                            className="h-14 w-full object-contain"
                            src={cropImage || '/assets/default-product.png'}
                            alt=""
                          />
                        </div>
                        <div className="grid items-center">{cropName}</div>
                        <div className="hidden w-full items-center gap-1.5">
                          <Input
                            {...register(`cropsPrices.${index}.cropId` as const)}
                            className={cn(
                              'focus-visible:ring-primary',
                              cropId && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            type="text"
                            placeholder="Enter crop name"
                            disabled
                          />
                          {cropId && <p className="form-error">{`${cropId.message}`}</p>}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.lowPrice` as const, {
                              onChange: () => trigger(`cropsPrices.${index}.lowPrice` as const),
                              onBlur: () => trigger(`cropsPrices.${index}.lowPrice` as const),
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              lowPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="Low Price"
                          />
                          {lowPrice && <p className="form-error">{`${lowPrice.message}`}</p>}
                        </div>
                        <div>-</div>
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.highPrice` as const, {
                              onChange: () => {
                                trigger(`cropsPrices.${index}.highPrice` as const);
                                trigger(`cropsPrices.${index}.lowPrice` as const);
                              },
                              onBlur: () => {
                                trigger(`cropsPrices.${index}.highPrice` as const);
                                trigger(`cropsPrices.${index}.lowPrice` as const);
                              },
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              highPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="High Price"
                          />
                          {highPrice && <p className="form-error">{`${highPrice.message}`}</p>}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.lowBaptcPrice` as const, {
                              onChange: () => trigger(`cropsPrices.${index}.lowBaptcPrice` as const),
                              onBlur: () => trigger(`cropsPrices.${index}.lowBaptcPrice` as const),
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              lowBaptcPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="Low Price"
                          />
                          {lowBaptcPrice && <p className="form-error">{`${lowBaptcPrice.message}`}</p>}
                        </div>
                        <div>-</div>
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.highBaptcPrice` as const, {
                              onChange: () => {
                                trigger(`cropsPrices.${index}.highBaptcPrice` as const);
                                trigger(`cropsPrices.${index}.lowBaptcPrice` as const);
                              },
                              onBlur: () => {
                                trigger(`cropsPrices.${index}.highBaptcPrice` as const);
                                trigger(`cropsPrices.${index}.lowBaptcPrice` as const);
                              },
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              highBaptcPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="High Price"
                          />
                          {highBaptcPrice && <p className="form-error">{`${highBaptcPrice.message}`}</p>}
                        </div>
                      </div>
                    </TableCell>

                    <TableCell>
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.lowNvatPrice` as const, {
                              onChange: () => trigger(`cropsPrices.${index}.lowNvatPrice` as const),
                              onBlur: () => trigger(`cropsPrices.${index}.lowNvatPrice` as const),
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              lowNvatPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="Low Price"
                          />
                          {lowNvatPrice && <p className="form-error">{`${lowNvatPrice.message}`}</p>}
                        </div>
                        <div>-</div>
                        <div className="flex-1">
                          <Input
                            type="text"
                            {...register(`cropsPrices.${index}.highNvatPrice` as const, {
                              onChange: () => {
                                trigger(`cropsPrices.${index}.highNvatPrice` as const);
                                trigger(`cropsPrices.${index}.lowNvatPrice` as const);
                              },
                              onBlur: () => {
                                trigger(`cropsPrices.${index}.highNvatPrice` as const);
                                trigger(`cropsPrices.${index}.lowNvatPrice` as const);
                              },
                            })}
                            className={cn(
                              'focus-visible:ring-primary',
                              highNvatPrice && 'border-red-500 focus-visible:ring-red-500',
                            )}
                            placeholder="High Price"
                          />
                          {highNvatPrice && <p className="form-error">{`${highNvatPrice.message}`}</p>}
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </ScrollArea>
      </form>
    </div>
  );
}
