'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon, Plus, X } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector from '@/components/ui/multiple-selector';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';

const OPTION_NATURAL_DISASTER = [
  {
    label: 'Excessive Rainfall',
    value: 'Excessive Rainfall',
  },
  {
    label: 'Excessive Windspeed',
    value: 'Excessive Windspeed',
  },
];

export default function FarmInsurance() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const { getAllCrops, getSeeds, getFertilizer, getChemicals, chemOptions, fertilizers } = usePublic();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const govImg = data?.farmer?.farmerInsurance?.insurance_location_plan?.value;
  const govImgSplit = govImg?.split('/');
  const govName = govImgSplit ? govImgSplit[govImgSplit.length - 1] : '';

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    watch,
  } = useForm({
    defaultValues: {
      naturalDisasterCover:
        data?.farmer?.farmerInsurance?.natural_disaster_cover?.value
          ?.split(',')
          .map((v) => ({ label: v.trim(), value: v.trim() })) || [],
      multiRiskCover: data?.farmer?.farmerInsurance?.multi_risk_cover?.value || '',
      desiredAmountCover: data?.farmer?.farmerInsurance?.desired_amount_cover?.value || '',
      additionalAmountCover: data?.farmer?.farmerInsurance?.additional_amount_cover?.value || '',
      transplantingDate: data?.farmer?.farmerInsurance?.transplanting_date?.value || '',
      harvestDate: data?.farmer?.farmerInsurance?.harvest_date?.value || '',
      sowingDate: data?.farmer?.farmerInsurance?.sowing_date?.value || '',
      seedbedding: data?.farmer?.farmerInsurance?.seedbedding?.data?.value || [
        {
          option: '',
          quantity: '',
          cost: '',
        },
      ],
      planting: data?.farmer?.farmerInsurance?.planting?.data?.value || [
        {
          option: '',
          specify: '',
          quantity: '',
          cost: '',
        },
      ],
      plantCare: data?.farmer?.farmerInsurance?.plant_care?.data?.value || [
        {
          option: '',
          specify: '',
          quantity: '',
          cost: '',
        },
      ],
      insurancePremium: data?.farmer?.farmerInsurance?.insurance_premium?.value || '',
      insuranceLocationPlan: '',
    },
  });
  const watchMultiRiskCover = watch('multiRiskCover');
  const watchDesiredAmountCover = watch('desiredAmountCover');
  const watchAdditionalAmountCover = watch('additionalAmountCover');
  const seedbedding = useFieldArray({
    name: 'seedbedding',
    control,
  });
  const planting = useFieldArray({
    name: 'planting',
    control,
  });
  const watchPlanting = watch('planting');
  const plantCare = useFieldArray({
    name: 'plantCare',
    control,
  });
  const watchPlantCare = watch('plantCare');

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      insuranceLocationPlan:
        _data.insuranceLocationPlan && _data.insuranceLocationPlan.length > 0 ? _data.insuranceLocationPlan[0] : null,
      naturalDisasterCover: _data.naturalDisasterCover.map((s) => s.value).join(','),
      userId: data.farmer.user_id.value,
    };

    if (updatedData.seedbedding.length === 1) {
      if (!updatedData.seedbedding[0].option) {
        delete updatedData.seedbedding;
      }
    }

    if (updatedData.planting.length === 1) {
      if (!updatedData.planting[0].option) {
        delete updatedData.planting;
      }
    }

    if (updatedData.plantCare.length === 1) {
      if (!updatedData.plantCare[0].option) {
        delete updatedData.plantCare;
      }
    }

    console.log('Farm Ensurance: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  useEffect(() => {
    Promise.all([getAllCrops(), getSeeds(), getFertilizer(), getChemicals()]);
  }, []);

  return (
    <form className="" id={PROFILE_TAB[6].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="font-dmSans text-xl font-bold text-primary">Insurance Information</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Natural Disaster Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="naturalDisasterCover" className="pb-1 font-normal">
            Natural Disaster Cover
          </Label>
          <Controller
            control={control}
            name="naturalDisasterCover"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_NATURAL_DISASTER}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
                disabled={!gState.accountProfileIsEdit.value}
              />
            )}
          />
          {errors.naturalDisasterCover && <p className="form-error">{`${errors.naturalDisasterCover.message}`}</p>}
        </div>

        {/* Multi Risk Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="multiRiskCover" className="pb-1 font-normal">
            Multi Risk Cover
          </Label>
          <Input
            {...register('multiRiskCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Multi Risk Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.multiRiskCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Multi Risk Cover"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.multiRiskCover && <p className="form-error">{`${errors.multiRiskCover.message}`}</p>}
        </div>

        {/* Desired Amount Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="desiredAmountCover" className="pb-1 font-normal">
            Desired Amount Cover
          </Label>
          <Input
            {...register('desiredAmountCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Desired Amount Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.desiredAmountCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Desired Amount Cover"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.desiredAmountCover && <p className="form-error">{`${errors.desiredAmountCover.message}`}</p>}
        </div>

        {/* Additional Amount Cover */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="additionalAmountCover" className="pb-1 font-normal">
            Additional Amount Cover
          </Label>
          <Input
            {...register('additionalAmountCover', {
              required: false,
              validate: {
                isGreaterThanZero: (v) =>
                  v ? (Number(v) || 0) > 0 || 'Additional Amount Cover must be greater than 0' : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.additionalAmountCover && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Additional Amount Cover"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.additionalAmountCover && <p className="form-error">{`${errors.additionalAmountCover.message}`}</p>}
        </div>

        {/* Total Amount */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="additionalAmountCover" className="pb-1 font-normal">
            Total Amount
          </Label>
          <Input
            value={(
              Number(watchMultiRiskCover) +
              Number(watchDesiredAmountCover) +
              Number(watchAdditionalAmountCover)
            ).toLocaleString()}
            disabled
            className={cn('focus-visible:ring-primary')}
            type="text"
            placeholder="0"
          />
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Harvest Information</div>
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Date of transplanting or direct seedling */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="transplantingDate" className="pb-1 font-normal">
            Date of transplanting or direct seedling
          </Label>
          <Input
            {...register('transplantingDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.transplantingDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Date of transplanting or direct seedling"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.transplantingDate && <p className="form-error">{`${errors.transplantingDate.message}`}</p>}
        </div>

        {/* Harvest Date */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="harvestDate" className="pb-1 font-normal">
            Harvest Date
          </Label>
          <Input
            {...register('harvestDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.harvestDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Harvest Date"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.harvestDate && <p className="form-error">{`${errors.harvestDate.message}`}</p>}
        </div>

        {/* Sowing Date */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="sowingDate" className="pb-1 font-normal">
            Sowing Date
          </Label>
          <Input
            {...register('sowingDate')}
            className={cn(
              'focus-visible:ring-primary',
              errors.sowingDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Sowing Date"
            disabled={!gState.accountProfileIsEdit.value}
          />
          {errors.sowingDate && <p className="form-error">{`${errors.sowingDate.message}`}</p>}
        </div>
      </div>

      {/* Farm & Plan Budget Activity */}
      <div className="font-dmSans text-xl font-bold text-primary">Farm & Plan Budget Activity</div>
      <div className="space-y-4 pr-14">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {seedbedding.fields.map((field, index) => {
            const errorForField = errors?.seedbedding?.[index];

            return (
              <div key={field.id} className="grid items-start gap-4 pb-3 pt-7 sm:grid-cols-2 xl:grid-cols-3">
                {/* Seedbedding */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`seedbedding.${index}.option`} className="pb-1 font-normal">
                    Seedbedding
                  </Label>
                  <Controller
                    control={control}
                    name={`seedbedding.${index}.option` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.option &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select option" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="SEEDLING MATERIAL">SEEDLING MATERIAL</SelectItem>
                            <SelectItem value="LABOR">LABOR</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.option && <p className="form-error">{`${errorForField?.option?.message}`}</p>}
                </div>

                {/* Quantity */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`seedbedding.${index}.quantity`} className="pb-1 font-normal">
                    Quantity
                  </Label>
                  <Input
                    {...register(`seedbedding.${index}.quantity` as const, {
                      required: false,
                      validate: {
                        isPositive: (value) =>
                          value
                            ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Quantity must be greater than 0'
                            : true,
                      },
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.quantity && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Quantity"
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                  {errorForField?.quantity && <p className="form-error">{`${errorForField?.quantity?.message}`}</p>}
                </div>

                {/* Cost */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`seedbedding.${index}.cost`} className="pb-1 font-normal">
                    Cost
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`seedbedding.${index}.cost` as const, {
                        required: false,
                        validate: {
                          isPositive: (value) =>
                            value
                              ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Cost must be greater than 0'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.cost && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter cost"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => seedbedding.remove(index)}
                        disabled={!gState.accountProfileIsEdit.value}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.cost && <p className="form-error">{`${errorForField?.cost?.message}`}</p>}
                </div>
              </div>
            );
          })}
        </div>

        {/* Add More Vehicle */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              seedbedding.append({
                option: '',
                quantity: '',
                cost: '',
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Seedbedding</span>
          </Button>
        </div>
      </div>

      {/* Planting */}
      <div className="font-dmSans text-xl font-bold text-primary">Planting</div>
      <div className="space-y-4 pr-14">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {planting.fields.map((field, index) => {
            const errorForField = errors?.planting?.[index];

            return (
              <div key={field.id} className="grid gap-4">
                <div className="grid items-start gap-4 pb-3 pt-7 sm:grid-cols-2 xl:grid-cols-3">
                  {/* Planting */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`planting.${index}.option`} className="pb-1 font-normal">
                      Planting
                    </Label>
                    <Controller
                      control={control}
                      name={`planting.${index}.option` as const}
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errorForField?.option &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select option" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="CHEMICAL">CHEMICAL</SelectItem>
                              <SelectItem value="FERTILIZER">FERTILIZER</SelectItem>
                              <SelectItem value="LABOR">LABOR</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errorForField?.option && <p className="form-error">{`${errorForField?.option?.message}`}</p>}
                  </div>

                  {/* Quantity */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`planting.${index}.quantity`} className="pb-1 font-normal">
                      Quantity
                    </Label>
                    <Input
                      {...register(`planting.${index}.quantity` as const, {
                        required: false,
                        validate: {
                          isPositive: (value) =>
                            value
                              ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Quantity must be greater than 0'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.quantity && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Quantity"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    {errorForField?.quantity && <p className="form-error">{`${errorForField?.quantity?.message}`}</p>}
                  </div>

                  {/* Cost */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`planting.${index}.cost`} className="pb-1 font-normal">
                      Cost
                    </Label>
                    <div className="relative">
                      <Input
                        {...register(`planting.${index}.cost` as const, {
                          required: false,
                          validate: {
                            isPositive: (value) =>
                              value
                                ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Cost must be greater than 0'
                                : true,
                          },
                        })}
                        className={cn(
                          'focus-visible:ring-primary',
                          errorForField?.cost && 'border-red-500 focus-visible:ring-red-500',
                        )}
                        type="text"
                        placeholder="Enter cost"
                        disabled={!gState.accountProfileIsEdit.value}
                      />
                      <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                        <Button
                          className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                          variant="outline"
                          size="icon"
                          onClick={() => planting.remove(index)}
                          disabled={!gState.accountProfileIsEdit.value}
                        >
                          <X className="size-5" />
                        </Button>
                      </div>
                    </div>
                    {errorForField?.cost && <p className="form-error">{`${errorForField?.cost?.message}`}</p>}
                  </div>
                </div>

                {(watchPlanting[index]['option'] === 'CHEMICAL' || watchPlanting[index]['option'] === 'FERTILIZER') && (
                  <div className="grid grid-cols-3 items-start gap-4">
                    {/* Specify */}
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor={`planting.${index}.specify`} className="pb-1 font-normal">
                        {watchPlanting[index]['option'] === 'CHEMICAL' ? 'Chemical' : 'Fertilizer'}
                      </Label>
                      <Controller
                        control={control}
                        name={`planting.${index}.specify` as const}
                        render={({ field: { onChange, onBlur, value, ref } }) => (
                          <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                            <SelectTrigger
                              className={cn(
                                'focus-visible:ring-primary',
                                errorForField?.specify &&
                                  'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                              )}
                            >
                              <SelectValue
                                placeholder={`Select ${watchPlanting[index]['option'] === 'CHEMICAL' ? 'chemical' : 'fertilizer'}`}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {watchPlanting[index]['option'] === 'CHEMICAL' &&
                                chemOptions.get({ noproxy: true }).map((option) => (
                                  <SelectGroup key={option.label}>
                                    <SelectLabel>{option.label}</SelectLabel>
                                    {option.options.map((opt) => {
                                      const splitted = opt.split('-');
                                      const id = splitted[0];
                                      const name = splitted[1];

                                      return (
                                        <SelectItem key={id} value={name}>
                                          {name}
                                        </SelectItem>
                                      );
                                    })}
                                  </SelectGroup>
                                ))}

                              {watchPlanting[index]['option'] === 'FERTILIZER' && (
                                <SelectGroup>
                                  {fertilizers.get({ noproxy: true }).map((fertilizer) => (
                                    <SelectItem key={fertilizer.id} value={fertilizer.name}>
                                      {fertilizer.name}
                                    </SelectItem>
                                  ))}
                                </SelectGroup>
                              )}
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errorForField?.specify && <p className="form-error">{`${errorForField?.specify?.message}`}</p>}
                    </div>
                  </div>
                )}

                {watchPlanting[index]['option'] === 'LABOR' && (
                  <div className="grid grid-cols-3 items-start gap-4">
                    {/* Specify */}
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor={`planting.${index}.specify`} className="pb-1 font-normal">
                        Specify Labor
                      </Label>
                      <div className="relative">
                        <Input
                          {...register(`planting.${index}.specify` as const, {
                            required: false,
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.specify && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          type="text"
                          placeholder="Specify Labor"
                          disabled={!gState.accountProfileIsEdit.value}
                        />
                      </div>
                      {errorForField?.cost && <p className="form-error">{`${errorForField?.cost?.message}`}</p>}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Add More Planting */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              planting.append({
                option: '',
                specify: '',
                quantity: '',
                cost: '',
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Planting</span>
          </Button>
        </div>
      </div>

      {/* Plant Care */}
      <div className="font-dmSans text-xl font-bold text-primary">Plant Care</div>
      <div className="space-y-4 pr-14">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {plantCare.fields.map((field, index) => {
            const errorForField = errors?.plantCare?.[index];

            return (
              <div key={field.id} className="grid gap-4">
                <div className="grid items-start gap-4 pb-3 pt-7 sm:grid-cols-2 xl:grid-cols-3">
                  {/* Plant Care */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`plantCare.${index}.option`} className="pb-1 font-normal">
                      Plant Care
                    </Label>
                    <Controller
                      control={control}
                      name={`plantCare.${index}.option` as const}
                      render={({ field: { onChange, onBlur, value, ref } }) => (
                        <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                          <SelectTrigger
                            className={cn(
                              'focus-visible:ring-primary',
                              errorForField?.option &&
                                'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                            )}
                          >
                            <SelectValue placeholder="Select option" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectGroup>
                              <SelectItem value="TOP DRESSING">TOP DRESSING</SelectItem>
                              <SelectItem value="CHEMICAL">CHEMICAL</SelectItem>
                              <SelectItem value="LABOR">LABOR</SelectItem>
                            </SelectGroup>
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errorForField?.option && <p className="form-error">{`${errorForField?.option?.message}`}</p>}
                  </div>

                  {/* Quantity */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`plantCare.${index}.quantity`} className="pb-1 font-normal">
                      Quantity
                    </Label>
                    <Input
                      {...register(`plantCare.${index}.quantity` as const, {
                        required: false,
                        validate: {
                          isPositive: (value) =>
                            value
                              ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Quantity must be greater than 0'
                              : true,
                        },
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.quantity && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Quantity"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    {errorForField?.quantity && <p className="form-error">{`${errorForField?.quantity?.message}`}</p>}
                  </div>

                  {/* Cost */}
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label htmlFor={`plantCare.${index}.cost`} className="pb-1 font-normal">
                      Cost
                    </Label>
                    <div className="relative">
                      <Input
                        {...register(`plantCare.${index}.cost` as const, {
                          required: false,
                          validate: {
                            isPositive: (value) =>
                              value
                                ? (/^\d+(\.\d+)?$/.test(value) && Number(value) > 0) || 'Cost must be greater than 0'
                                : true,
                          },
                        })}
                        className={cn(
                          'focus-visible:ring-primary',
                          errorForField?.cost && 'border-red-500 focus-visible:ring-red-500',
                        )}
                        type="text"
                        placeholder="Enter cost"
                        disabled={!gState.accountProfileIsEdit.value}
                      />
                      <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                        <Button
                          className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                          variant="outline"
                          size="icon"
                          onClick={() => plantCare.remove(index)}
                          disabled={!gState.accountProfileIsEdit.value}
                        >
                          <X className="size-5" />
                        </Button>
                      </div>
                    </div>
                    {errorForField?.cost && <p className="form-error">{`${errorForField?.cost?.message}`}</p>}
                  </div>
                </div>

                {watchPlantCare[index]['option'] === 'CHEMICAL' && (
                  <div className="grid grid-cols-3 items-start gap-4">
                    {/* Specify */}
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor={`plantCare.${index}.specify`} className="pb-1 font-normal">
                        Chemicals
                      </Label>
                      <Controller
                        control={control}
                        name={`plantCare.${index}.specify` as const}
                        render={({ field: { onChange, onBlur, value, ref } }) => (
                          <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                            <SelectTrigger
                              className={cn(
                                'focus-visible:ring-primary',
                                errorForField?.specify &&
                                  'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                              )}
                            >
                              <SelectValue
                                placeholder={`Select ${watchPlantCare[index]['option'] === 'CHEMICAL' ? 'chemical' : 'fertilizer'}`}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {watchPlantCare[index]['option'] === 'CHEMICAL' &&
                                chemOptions.get({ noproxy: true }).map((option) => (
                                  <SelectGroup key={option.label}>
                                    <SelectLabel>{option.label}</SelectLabel>
                                    {option.options.map((opt) => {
                                      const splitted = opt.split('-');
                                      const id = splitted[0];
                                      const name = splitted[1];

                                      return (
                                        <SelectItem key={id} value={name}>
                                          {name}
                                        </SelectItem>
                                      );
                                    })}
                                  </SelectGroup>
                                ))}
                            </SelectContent>
                          </Select>
                        )}
                      />
                      {errorForField?.specify && <p className="form-error">{`${errorForField?.specify?.message}`}</p>}
                    </div>
                  </div>
                )}

                {(watchPlantCare[index]['option'] === 'LABOR' ||
                  watchPlantCare[index]['option'] === 'TOP DRESSING') && (
                  <div className="grid grid-cols-3 items-start gap-4">
                    {/* Specify */}
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor={`plantCare.${index}.specify`} className="pb-1 font-normal">
                        Specify
                      </Label>
                      <div className="relative">
                        <Input
                          {...register(`plantCare.${index}.specify` as const, {
                            required: false,
                          })}
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.specify && 'border-red-500 focus-visible:ring-red-500',
                          )}
                          type="text"
                          placeholder="Specify"
                          disabled={!gState.accountProfileIsEdit.value}
                        />
                      </div>
                      {errorForField?.cost && <p className="form-error">{`${errorForField?.cost?.message}`}</p>}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Add More Plant Care */}
        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              plantCare.append({
                option: '',
                specify: '',
                quantity: '',
                cost: '',
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Plant Care</span>
          </Button>
        </div>
      </div>

      {/* Insurance */}
      <div className="my-6 grid items-start gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Insurance Premium */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="insurancePremium" className="pb-1 font-normal">
            Insurance Premium
          </Label>
          <Input
            {...register('insurancePremium')}
            className={cn('focus-visible:ring-primary')}
            type="text"
            placeholder="Enter Insurance Premium"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </div>

        {/* Insurance Location Plan */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="insuranceLocationPlan" className="pb-1 font-normal">
            Insurance Location Plan
          </Label>
          <Input
            {...register('insuranceLocationPlan')}
            className={cn('focus-visible:ring-primary')}
            type="file"
            placeholder="Enter Insurance Premium"
            disabled={!gState.accountProfileIsEdit.value}
          />

          {govImg && (
            <div className="">
              <a className="flex items-center gap-2 text-primary hover:underline" href={govImg} target="_blank">
                <PaperclipIcon className="size-4" />
                {govName}
              </a>
            </div>
          )}
        </div>
      </div>
    </form>
  );
}
