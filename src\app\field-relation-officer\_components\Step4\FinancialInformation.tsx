import React, { useEffect } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  useFormContext,
  UseFormGetValues,
  UseFormRegister,
  UseFormSetValue,
} from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

import { OPTIONS_SOURCE_OF_FUNDS } from '@/app/admin/(accounts)/account-info/_components/tab-content/account-profile/Enums';
import { cn } from '@/lib/utils';

import { TBusinessInfoSchema } from '../../schemas';

interface IFinancialInformationProps {
  register: UseFormRegister<TBusinessInfoSchema>;
  control: Control<TBusinessInfoSchema>;
  errors: FieldErrors<TBusinessInfoSchema>;
}

const FinancialInformation = ({ register, control, errors }: IFinancialInformationProps) => {
  return (
    <div>
      <FormTitle title="Financial Information" />
      <div className="grid items-start gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="sourceOfFunds" label="Source of Income" errors={errors} required>
          <Controller
            control={control}
            name="sourceOfFunds"
            render={({ field: { onChange, value } }) => {
              const selectedValues: string[] = Array.isArray(value) ? value : [];

              const toggleValue = (val: string) => {
                let newValue = [...selectedValues];

                if (val === 'OTHERS') {
                  const hasOthers = newValue.includes('OTHERS');
                  if (hasOthers) {
                    newValue = newValue.filter((v, i) => v !== 'OTHERS' && i !== newValue.indexOf('OTHERS') + 1);
                  } else {
                    newValue.push('OTHERS', '');
                  }
                } else {
                  if (newValue.includes(val)) {
                    newValue = newValue.filter((v) => v !== val);
                  } else {
                    newValue.push(val);
                  }
                }

                onChange(newValue);
              };

              const hasOthers = selectedValues.includes('OTHERS');
              const othersIndex = selectedValues.indexOf('OTHERS');
              const customValue =
                othersIndex >= 0 && selectedValues.length > othersIndex + 1 ? selectedValues[othersIndex + 1] : '';

              const displayText = (() => {
                if (othersIndex >= 0) {
                  const beforeOthers = selectedValues.slice(0, othersIndex);
                  const afterOthers = selectedValues.slice(othersIndex + 2);
                  const othersWithCustom = customValue ? `OTHERS (${customValue})` : 'OTHERS';
                  return [...beforeOthers, othersWithCustom, ...afterOthers].join(', ') || 'Select income sources';
                } else {
                  return selectedValues.length > 0 ? selectedValues.join(', ') : 'Select income sources';
                }
              })();

              return (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      className="h-fit w-full justify-between whitespace-normal break-words text-left font-normal focus:outline-none"
                    >
                      {displayText}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent
                    side="bottom"
                    align="start"
                    sideOffset={4}
                    className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                    avoidCollisions={false}
                  >
                    <div className="overflow-auto">
                      <ScrollArea>
                        {OPTIONS_SOURCE_OF_FUNDS.map((option) => (
                          <div
                            key={option.value}
                            onClick={() => toggleValue(option.value)}
                            className="flex cursor-pointer items-center space-x-2 rounded p-2 text-sm hover:bg-gray-50"
                          >
                            <Checkbox checked={selectedValues.includes(option.value)} className="mr-2" />
                            {option.label}
                          </div>
                        ))}
                      </ScrollArea>
                    </div>

                    {hasOthers && (
                      <Input
                        autoFocus
                        placeholder="Please specify"
                        className="mt-2"
                        value={customValue}
                        onChange={(e) => {
                          const input = e.target.value.toUpperCase();
                          const updated = [...selectedValues];

                          if (othersIndex >= 0) {
                            updated[othersIndex + 1] = input;
                            onChange(updated);
                          }
                        }}
                      />
                    )}
                  </PopoverContent>
                </Popover>
              );
            }}
          />
        </FormField>

        <FormField name="monthlyGrossIncome" label="Monthly Gross Income" errors={errors} required>
          <Input
            {...register('monthlyGrossIncome', { required: 'Monthly Gross Income is required' })}
            type="number"
            min={0}
            placeholder="Enter Monthly Gross Income"
            className={cn('focus-visible:ring-primary', errors.monthlyGrossIncome && 'border-red-500')}
          />
        </FormField>
      </div>
    </div>
  );
};

export default FinancialInformation;
