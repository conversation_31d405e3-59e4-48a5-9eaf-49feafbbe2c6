'use client';

import { useHookstate } from '@hookstate/core';
import { Timeline, TimelineConnector, TimelineHeader, TimelineIcon, TimelineItem } from '@material-tailwind/react';
import { format } from 'date-fns';
import { CircleCheckIcon } from 'lucide-react';
import { Suspense, useEffect } from 'react';
import { MdRadioButtonChecked } from 'react-icons/md';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion-custom';
import { Badge } from '@/components/ui/badge';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';

import { PaymentStatus, PaymentStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { LoanStageLabel } from '@/lib/constants/enums';
import useFinance from '@/lib/hooks/admin/useFinance';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import FetchDetails from './_components/FetchDetails';

export default function LoanHistoryDetails() {
  const gStateP = useGlobalStatePersist();
  const account = useHookstate(gStateP.selected.account.info);
  const { creditScoreDetails, reqDetails, beforeDetails, duringDetails, afterDetails } = useFinance();

  const isBefore = useHookstate(false);
  const isDuring = useHookstate(false);
  const isAfter = useHookstate(false);

  useEffect(() => {
    if (!creditScoreDetails) return;

    const loanPeriodTracker = creditScoreDetails['loanPeriodTracker']?.get({ noproxy: true });
    console.log('loanPeriodTracker', loanPeriodTracker || 'No loan period tracker');

    if (!loanPeriodTracker) {
      // Before Loan Stage
      console.log('Loan is in Before Loan Stage');
      isBefore.set(true);
      isDuring.set(false);
      isAfter.set(false);
    } else {
      const { during_loan_end_at } = loanPeriodTracker;
      const now = new Date().toISOString();
      console.log('now', now);
      console.log('during_loan_end_at', during_loan_end_at);

      if (during_loan_end_at <= now) {
        // After Loan Stage
        console.log('Loan is in After Loan Stage');
        isAfter.set(true);
        isBefore.set(false);
        isDuring.set(false);
      } else {
        // During Loan Stage
        console.log('Loan is in During Loan Stage');
        isDuring.set(true);
        isBefore.set(false);
        isAfter.set(false);
      }
    }
  }, [creditScoreDetails]);

  return (
    <div className="space-y-6 p-6 md:p-12">
      {/* Data Fetcher */}
      <Suspense>
        <FetchDetails />
      </Suspense>

      {/* Title */}
      <div className="">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Loan History Details</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink href={`/admin/account-info/?id=${account['id'].value}&ait=credit_history`}>
                Credit History
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink
                href={`/admin/account-info/?id=${account['id'].value}&ait=credit_history&cht=loan_history`}
              >
                Loan History
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Loan History Details</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Card Details */}
      {reqDetails.value && (
        <div className="card">
          <dl className="grid gap-4 font-dmSans sm:grid-cols-2">
            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Start Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {format(reqDetails.created_at.value, 'MMM dd, yyyy')}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan End Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {format(reqDetails.due_at.value, 'MMM dd, yyyy')}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Term</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails.loan_term.value} days
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Credit Score Group</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails.creditScoreGroup.name.value}
                </dd>
              </div>
            </div>

            <div>
              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Payment Status</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  <Badge className={cn(PaymentStatusLabels[reqDetails.payment_status.value].color, 'min-w-max')}>
                    {PaymentStatusLabels[reqDetails.payment_status.value].label}
                  </Badge>
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Loan Balance</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {Number(reqDetails.user.wallet.credit.value).toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'PHP',
                  })}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Final Payment Date</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                  {reqDetails?.paid_at.value ? format(reqDetails.paid_at.value, 'MMM dd, yyyy') : '-'}
                </dd>
              </div>

              <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                <dt className="text-sm font-medium leading-6 text-slate-400">Repayment Behavior</dt>
                <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">-</dd>
              </div>
            </div>
          </dl>
        </div>
      )}

      {/* Loan Stages */}

      <div className="mt-6">
        <Timeline>
          <Accordion className="" type="single" collapsible>
            {isAfter && afterDetails['categories']?.get({ noproxy: true })?.length > 0 && (
              <AccordionItem className="" value="after">
                <TimelineItem className={cn('min-h-[5.4rem]')}>
                  <TimelineConnector className="!w-[78px] " />
                  <AccordionTrigger className="">
                    <TimelineHeader
                      className={cn(
                        'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                        isAfter.get({ noproxy: true }) ? 'bg-white' : 'bg-gray-200',
                      )}
                    >
                      <TimelineIcon
                        className="p-3"
                        variant="ghost"
                        color={isAfter.get({ noproxy: true }) ? 'orange' : 'green'}
                      >
                        {isAfter.get({ noproxy: true }) ? (
                          <MdRadioButtonChecked className="size-5" />
                        ) : (
                          <CircleCheckIcon className="size-5" />
                        )}
                      </TimelineIcon>
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          <div className="text-lg font-bold text-kitaph-primary">After Loan Stage</div>
                        </div>
                        <div className="text-lg font-bold text-kitaph-primary">
                          {afterDetails['computedScore']?.value}%
                        </div>
                      </div>
                    </TimelineHeader>
                  </AccordionTrigger>

                  <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                    {afterDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                      <div key={index}>
                        <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                          <div>{category.name}</div>
                          <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                        </div>
                        <div className="grid max-w-lg gap-1 pl-10">
                          {category['rules']?.map((rule, ruleIndex) => (
                            <div key={ruleIndex}>
                              <div className="flex justify-between">
                                <div>{rule.name}</div>
                                <div>{`${rule.computedScore}/${rule.score}`}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </AccordionContent>
                </TimelineItem>
              </AccordionItem>
            )}

            {(isDuring.get({ noproxy: true }) || isAfter.get({ noproxy: true })) &&
              duringDetails['categories']?.get({ noproxy: true })?.length > 0 && (
                <AccordionItem className="" value="during">
                  <TimelineItem className={cn('min-h-[5.4rem]')}>
                    <TimelineConnector className="!w-[78px] " />
                    <AccordionTrigger className="">
                      <TimelineHeader
                        className={cn(
                          'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                          isDuring.get({ noproxy: true }) ? 'bg-white' : 'bg-gray-200',
                        )}
                      >
                        <TimelineIcon
                          className="p-3"
                          variant="ghost"
                          color={isDuring.get({ noproxy: true }) ? 'orange' : 'green'}
                        >
                          {isDuring.get({ noproxy: true }) ? (
                            <MdRadioButtonChecked className="size-5" />
                          ) : (
                            <CircleCheckIcon className="size-5" />
                          )}
                        </TimelineIcon>
                        <div className="flex w-full items-center justify-between">
                          <div className="text-left">
                            <div className="text-lg font-bold text-kitaph-primary">During Loan Stage</div>
                          </div>
                          <div className="text-lg font-bold text-kitaph-primary">
                            {duringDetails['computedScore']?.value}%
                          </div>
                        </div>
                      </TimelineHeader>
                    </AccordionTrigger>

                    <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                      {duringDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                        <div key={index}>
                          <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                            <div>{category.name}</div>
                            <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                          </div>
                          <div className="grid max-w-lg gap-1 pl-10">
                            {category['rules']?.map((rule, ruleIndex) => (
                              <div key={ruleIndex}>
                                <div className="flex justify-between">
                                  <div>{rule.name}</div>
                                  <div>{`${rule.computedScore}/${rule.score}`}</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </AccordionContent>
                  </TimelineItem>
                </AccordionItem>
              )}

            {beforeDetails['categories']?.get({ noproxy: true })?.length > 0 && (
              <AccordionItem className="" value="before">
                <TimelineItem className={cn('min-h-[5.4rem]')}>
                  <AccordionTrigger className="">
                    <TimelineHeader
                      className={cn(
                        'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                        isBefore.get({ noproxy: true }) ? 'bg-white' : 'bg-gray-200',
                      )}
                    >
                      <TimelineIcon
                        className="p-3"
                        variant="ghost"
                        color={isBefore.get({ noproxy: true }) ? 'orange' : 'green'}
                      >
                        {isBefore.get({ noproxy: true }) ? (
                          <MdRadioButtonChecked className="size-5" />
                        ) : (
                          <CircleCheckIcon className="size-5" />
                        )}
                      </TimelineIcon>
                      <div className="flex w-full items-center justify-between">
                        <div className="text-left">
                          <div className="text-lg font-bold text-kitaph-primary">Before Loan Stage</div>
                        </div>
                        <div className="text-lg font-bold text-kitaph-primary">
                          {beforeDetails['computedScore']?.value}%
                        </div>
                      </div>
                    </TimelineHeader>
                  </AccordionTrigger>

                  <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                    {beforeDetails['categories']?.get({ noproxy: true })?.map((category, index) => (
                      <div key={index}>
                        <div className="flex max-w-2xl items-center justify-between font-bold text-gray-700">
                          <div>{category.name}</div>
                          <div className="text-kitaph-primary">{`${category.computedScore} / ${category.score}`}</div>
                        </div>
                        <div className="grid max-w-lg gap-1 pl-10">
                          {category['rules']?.map((rule, ruleIndex) => (
                            <div key={ruleIndex}>
                              <div className="flex justify-between">
                                <div>{rule.name}</div>
                                <div>{`${rule.computedScore}/${rule.score}`}</div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </AccordionContent>
                </TimelineItem>
              </AccordionItem>
            )}
          </Accordion>
        </Timeline>
      </div>
    </div>
  );
}
