'use client';

import React from 'react';

import { FormDimensions } from '@/lib/constants/enums';

interface PersonData {
  first_name: string;
  middle_name?: string;
  last_name: string;
}

interface Page3Props {
  data: PersonData;
}

const Page3: React.FC<Page3Props> = ({ data }) => {
  const currentDate = new Date();
  const currentDay = currentDate.getDate();
  const currentMonth = currentDate.toLocaleDateString('en-US', { month: 'long' });
  const currentYear = currentDate.getFullYear();

  return (
    <div className={`relative flex flex-col border bg-white p-20 print:border-none ${FormDimensions.LEGAL}`}>
      <div className="space-y-6 text-sm">
        {/* Section 8 continuation */}
        <div className="ml-4">
          b. Ang mga indibidwal na farm data ay hindi maaaring ibahagi sa mga third-party kung walang paunang pahintulot
          mula sa Partner-Farmer, maliban kung ito ay kinakailangan ng batas, mga regulatory authorities o ng LANDBANK,
          bilang bahagi ng Lending Program
        </div>

        {/* Section 9 */}
        <div className="mb-6">
          <h3 className="mb-2 font-bold">9. Pag-aayos ng Hindi Pagkakaunawaan</h3>
          <p className="mb-2 text-justify">
            Ang anumang hindi pagkakaunawaan ay maaari lamang ayusin, base sa mga panuntunan na nakasaad sa PTMA ng
            parehong partido.
          </p>
        </div>

        {/* Section 10 */}
        <div className="mb-6">
          <h3 className="mb-2 font-bold">10. Pagtatapos ng Kasunduan</h3>
          <p className="mb-2 text-justify">
            Ang kasunduang ito ay magiging bahagi na rin ng PTMA at maaari lamang tapusin, alinsunod sa mga
            napagkasunduan sa PTMA.
          </p>
        </div>

        {/* Section 11 */}
        <div className="pb-8">
          <h3 className="mb-3 font-bold">11. Miscellaneous Provisions</h3>
          <div className="mb-6 ml-4 space-y-2">
            <div>
              a. Ang anumang bahagi ng Kasunduang ito na mapapawalang-bisa dahil ito ay salungat sa Batas ng Republika
              ng Pilipinas ay hindi makakaapekto sa bisa ng ibang bahagi ng Kasunduan ito at ng PTMA, at mananatiling
              may-bisa;
            </div>
            <div>
              b. Ang Kasunduang ito ay magiging epektibo sa parehong partido, pari na rin sa kanilang mga kahalili at
              mga tagapagmana;
            </div>
            <div>c. Ang Kasunduang ito ay magiging epektibo sa sandalling ito ay mapirmahan ng parehong partido.</div>
          </div>
        </div>

        {/* Date and Signature Section */}
        <div className="mb-8 text-sm">
          <div className="mb-6">
            <span>Nilagdaan ngayong ika-</span>
            <span className="mx-2 border-b border-black px-4 text-center"></span>
            <span>araw ng</span>
            <span className="mx-2 border-b border-black px-8 text-center"></span>
            <span>, {currentYear} sa</span>
            <span className="mx-2 border-b border-black px-12 text-center"></span>
            <span>.</span>
          </div>
        </div>

        {/* Signature Blocks */}
        <div className="flex justify-between">
          {/* KITA AGRITECH CORP */}
          <div className="w-1/2 pr-8">
            <div className="mb-2 text-center font-bold">KITA AGRITECH CORP</div>
            <div className="mb-1 text-sm">Ni:</div>
            <div className="mb-8 h-16 border-b border-black"></div>
            <div className="text-center text-sm">Earwin A. Belen, MSc., R.Agr.</div>
          </div>

          {/* Partner-Farmer */}
          <div className="w-1/2 pl-8">
            <div className="mb-2 text-center font-bold">Partner-Farmer</div>
            <div className="mb-1 text-sm">Ni:</div>
            <div className="mb-8 h-16 border-b border-black"></div>
            <div className="text-center text-sm capitalize">
              {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim() ||
                '(Name of Farmer)'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page3;
