'use client';

import { Control, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

interface IPhysicalAttributesFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function PhysicalAttributesForm({ register, control, errors }: IPhysicalAttributesFormProps) {
  const gState = useGlobalState();

  return (
    <div>
      <FormTitle title="Physical Attributes" className="mt-6" />

      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* Height */}
        <FormField name="height" label="Height (cm)" errors={errors}>
          <Input
            {...register('height', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (v || 0) > 0 || 'Height must be greater than 0 cm' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.height && 'border-red-500 focus-visible:ring-red-500')}
            type="number"
            min={0}
            placeholder="Enter Height"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Weight */}
        <FormField name="weight" label="Weight (kg)" errors={errors}>
          <Input
            {...register('weight', {
              required: false,
              validate: {
                isGreaterThanZero: (v) => (v ? (v || 0) > 0 || 'Weight must be greater than 0 kg' : true),
              },
            })}
            className={cn('focus-visible:ring-primary', errors.weight && 'border-red-500 focus-visible:ring-red-500')}
            type="number"
            min={0}
            placeholder="Enter Weight"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>
      </div>
    </div>
  );
}
