export default function Page1({ data }) {
  const address = data && data['address'] ? JSON.parse(data['address']) : {};

  const now = new Date();
  const day = now.getDate();
  const month = now.toLocaleString('default', { month: 'long' });
  const year = now.getFullYear() % 100;

  return (
    <div className="relative mx-auto flex h-[11in] w-[8.5in] flex-col border bg-contain bg-top bg-no-repeat px-8 py-12 text-[14px] capitalize leading-relaxed text-gray-900 print:border-none">
      <div className="mx-auto max-w-[600px]">
        <h1 className="mb-6 text-center text-lg font-bold uppercase">FARM LAND LEASE AGREEMENT</h1>

        <p className="mb-4 font-bold">KNOW ALL MEN BY THESE PRESENTS:</p>

        <p className="mb-4">
          This Farm Land Lease Agreement is made and entered into this{' '}
          <span className="inline-block w-8 border-b border-black text-center font-bold">{day}</span> day of
          <span className="inline-block w-24 border-b border-black text-center font-bold">{month}</span>,<br />
          20
          <span className="inline-block border-b border-black font-bold">{year}</span>, by and between:
        </p>
        <div className="mx-auto w-[500px]">
          {/* LESSOR */}
          <p className="mb-2">
            <b>LESSOR:</b> Mr./Ms./Mrs. <span className="inline-block w-52 border-b border-black"></span>, Filipino, of
            legal age, and resident of <span className="inline-block w-64 border-b border-black"></span>, hereinafter
            referred to as “LESSOR”.
          </p>

          <p className="mb-4 text-center">- AND -</p>

          {/* LESSEE */}
          <p className="mb-6">
            <b>LESSEE:</b> Mr./Ms./Mrs.{' '}
            <span className="inline-block border-b border-black px-4 text-center">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</span>
            , Filipino, of legal age, and resident of{' '}
            <span className="border-b border-black px-4 text-center">
              {' '}
              {`${address?.addressHouseNumber}, ${address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}, ${address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ''}, ${address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''}`}
            </span>
            , hereinafter referred to as “LESSEE”.
          </p>
        </div>

        {/* AGREEMENT TERMS */}
        <h2 className="mb-2 font-bold">AGREEMENT TERMS</h2>

        <ol className="ml-5 list-decimal space-y-3">
          <li>
            <span className="font-bold">Land and Farming Details</span>
            <ol className="ml-5 list-[lower-alpha] space-y-1">
              <li>
                The LESSOR agrees to lease to the LESSEE a farm lot located at{' '}
                <span className="inline-block w-64 border-b border-black"></span> covering{' '}
                <span className="inline-block w-24 border-b border-black"></span> hectares.
              </li>
              <li>
                The land is covered by the following document/s (check applicable) and is/are attached to this Agreement
                (ANNEX A):
                <div className="ml-12">
                  <div>
                    □ CTC No.: <div className="inline-block w-[27rem] border-b border-black"></div>
                  </div>
                  <div>
                    □ Tax Declaration No.: <div className="inline-block w-[23rem] border-b border-black"></div>
                  </div>
                  <div>
                    □ Plan No.: <div className="inline-block w-[27rem] border-b border-black"></div>
                  </div>
                </div>
              </li>
              <li>
                The boundaries of the land are described in ANNEX A, which forms an integral part of this agreement.
              </li>
              <li>
                The LESSEE shall use the farm lot for the following purpose/s and/or crop/s:
                <div className="ml-12">
                  <div>□ Palay / Rice □ Mais / Corn</div>
                  <div>
                    □ Vegetables: <div className="inline-block w-[27rem] border-b border-black"></div>
                  </div>
                  <div>
                    □ Fruits: <div className="inline-block w-[29rem] border-b border-black"></div>
                  </div>
                  <div>
                    □ Others: <div className="inline-block w-[29rem] border-b border-black"></div>
                  </div>
                </div>
              </li>
            </ol>
          </li>

          <li>
            <span className="font-bold">Lease Duration</span>
            This lease starts from the date stated above and will automatically continue each agricultural year unless
            terminated or modified as per applicable laws and regulations.
          </li>
        </ol>
      </div>
    </div>
  );
}
