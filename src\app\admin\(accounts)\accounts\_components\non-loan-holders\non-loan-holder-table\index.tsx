'use client';

import {
  ColumnFiltersState,
  flexRender,
  getCoreRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  RowData,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { DataTablePagination } from '@/components/layout/table/table-pagination';
import { DataTablePaginationMeta } from '@/components/layout/table/table-pagination-meta';
import { DataTableToolbar } from '@/components/layout/table/table-toolbar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { DashboardStat } from '@/app/admin/marketplace/_components/orders-table';
import { RatingType } from '@/lib/constants/enums';
import useMember from '@/lib/hooks/admin/useMember';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

declare module '@tanstack/react-table' {
  interface TableMeta<TData extends RowData> {
    getRowClicked: (rowIndex: any) => void;
  }
}

export function NonLoanHolderTable({ columns, data, metadata = null }) {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const router = useRouter();
  const { nonLoanHolderDashboard, pagination } = useMember();

  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    getFacetedUniqueValues: getFacetedUniqueValues(),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
    meta: {
      getRowClicked: (row) => {
        const data = row.original;
        console.log('row clicked', data);

        router.push(`/admin/account-info/?id=${data.farmer.user_id}`);
      },
    },
  });

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4 pb-4 sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-6">
        {/* All Non-Loan */}
        <DashboardStat
          value={nonLoanHolderDashboard.allNonLoan.value}
          label="All Non-Loan"
          className="bg-nonloanHolder-all focus:shadow-xl focus:shadow-nonloanHolder-all/60"
          img="/assets/credit-rating/all-non-loan.png"
          onClick={() => pagination.ratings.set([])}
        />

        {/* Excellent */}
        <DashboardStat
          value={nonLoanHolderDashboard.excellentNonLoan.value}
          label="Excellent"
          className="bg-nonloanHolder-excellent focus:shadow-xl focus:shadow-nonloanHolder-excellent/60"
          img="/assets/credit-rating/excellent.png"
          onClick={() => pagination.ratings.set([RatingType.EXCELLENT])}
        />

        {/* Very Good */}
        <DashboardStat
          value={nonLoanHolderDashboard.veryGoodNonLoan.value}
          label="Very Good"
          className="bg-nonloanHolder-veryGood focus:shadow-xl focus:shadow-nonloanHolder-veryGood/60"
          img="/assets/credit-rating/very-good.png"
          onClick={() => pagination.ratings.set([RatingType.VERY_GOOD])}
        />

        {/* Good */}
        <DashboardStat
          value={nonLoanHolderDashboard.goodNonLoan.value}
          label="Good"
          className="bg-nonloanHolder-good focus:shadow-xl focus:shadow-nonloanHolder-good/60"
          img="/assets/credit-rating/good.png"
          onClick={() => pagination.ratings.set([RatingType.GOOD])}
        />

        {/* Fair */}
        <DashboardStat
          value={nonLoanHolderDashboard.fairNonLoan.value}
          label="Fair"
          className="bg-nonloanHolder-fair focus:shadow-xl focus:shadow-nonloanHolder-fair/60"
          img="/assets/credit-rating/fair.png"
          onClick={() => pagination.ratings.set([RatingType.FAIR])}
        />

        {/* Poor */}
        <DashboardStat
          value={nonLoanHolderDashboard.poorNonLoan.value}
          label="Poor"
          className="bg-nonloanHolder-poor focus:shadow-xl focus:shadow-nonloanHolder-poor/60"
          img="/assets/credit-rating/poor.png"
          onClick={() => pagination.ratings.set([RatingType.POOR])}
        />
      </div>

      <DataTableToolbar id="account-nonloan" table={table} meta={metadata} />

      <div className="rounded-md border bg-white">
        <HorizontalScrollBar>
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    return (
                      <TableHead key={header.id}>
                        {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="hover:cursor-pointer"
                    key={row.id}
                    data-state={row.getIsSelected() && 'selected'}
                    onClick={(event) => {
                      if ((event.target as HTMLElement).tagName.toLowerCase() !== 'button')
                        table.options.meta?.getRowClicked?.(row);
                    }}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id}>{flexRender(cell.column.columnDef.cell, cell.getContext())}</TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={columns.length} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </HorizontalScrollBar>
      </div>

      {metadata ? (
        <DataTablePaginationMeta
          table={table}
          meta={metadata}
          onChangePageSize={(pageSize) => {
            gState.admin.pagination.nonLoanHolder.pageSize.set(pageSize);
          }}
          onChangePage={(page) => {
            gState.admin.pagination.nonLoanHolder.page.set(page);
          }}
          all={metadata.total}
        />
      ) : (
        <DataTablePagination table={table} />
      )}
    </div>
  );
}
