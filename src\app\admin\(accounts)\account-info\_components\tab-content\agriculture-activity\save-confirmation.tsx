'use client';

import { useHookstate } from '@hookstate/core';

import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogClose, DialogContent, DialogFooter } from '@/components/ui/dialog';

export function SaveConfirmation({ state, onSave = () => {} }) {
  // use this if you want to control the alert dialog programatically
  const dialogState = useHookstate(state);

  return (
    <Dialog open={dialogState.value} onOpenChange={dialogState.set}>
      <DialogContent className="font-sans sm:max-w-lg">
        <div className="text-center">
          <div>
            <img className="mx-auto" src="/assets/undraw/confirm.png" alt="" />
          </div>
          <div className="my-4 text-xl font-bold">Confirmation</div>
          <div>Do you confirm your intention to save the changes?</div>
        </div>

        <DialogFooter className="mt-4 sm:justify-between">
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" variant="outline">
              Review Items
            </Button>
          </DialogClose>
          <DialogClose asChild>
            <Button type="button" size="lg" className="px-12" onClick={onSave}>
              Yes, Save
            </Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
