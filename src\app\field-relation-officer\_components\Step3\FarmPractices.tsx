'use client';

import { useEffect } from 'react';
import { Control, Controller, FieldErrors, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import usePublic from '@/lib/hooks/usePublic';
import { cn } from '@/lib/utils';

import { FARM_IMPLEMENTS, FERTILIZER_CHEMICAL_USED_OPTIONS, WATER_SOURCE } from '../../constants';
import { TFarmInformationSchema } from '../../schemas';

interface IFarmPracticesProps {
  control: Control<TFarmInformationSchema>;
  errors: FieldErrors<TFarmInformationSchema>;
  register: UseFormRegister<TFarmInformationSchema>;
}

const FarmPractices = ({ control, errors, register }: IFarmPracticesProps) => {
  const { getAllCrops } = usePublic();

  useEffect(() => {
    getAllCrops();
  }, []);

  return (
    <div className="mt-6">
      <FormTitle title="Farm Practices" />
      <div className="grid items-start gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="averageYieldPerYear" label="Average Yield Per Year (Kilos or Tons)" errors={errors} required>
          <Input
            {...register('averageYieldPerYear', { required: 'Average Yield Per Year is required' })}
            placeholder="Enter number"
            className={cn(errors.averageYieldPerYear && 'border-red-500')}
            type="number"
            min={0}
          />
        </FormField>
        {/* Farm Practices */}
        <FormField name="waterSource" label="Water Source" errors={errors} required>
          <Controller
            control={control}
            name="waterSource"
            render={({ field: { onChange, value } }) => {
              const selectedValues: string[] = Array.isArray(value) ? value : [];

              const toggleValue = (val: string) => {
                let newValue = [...selectedValues];

                if (val === 'Others') {
                  const hasOthers = newValue.includes('Others');
                  if (hasOthers) {
                    newValue = newValue.filter((v, i) => v !== 'Others' && i !== newValue.indexOf('Others') + 1);
                  } else {
                    newValue.push('Others', '');
                  }
                } else {
                  if (newValue.includes(val)) {
                    newValue = newValue.filter((v) => v !== val);
                  } else {
                    newValue.push(val);
                  }
                }

                onChange(newValue);
              };

              const hasOthers = selectedValues.includes('Others');
              const othersIndex = selectedValues.indexOf('Others');
              const customValue =
                othersIndex >= 0 && selectedValues.length > othersIndex + 1 ? selectedValues[othersIndex + 1] : '';

              const displayText = (() => {
                if (othersIndex >= 0) {
                  const beforeOthers = selectedValues.slice(0, othersIndex);
                  const afterOthers = selectedValues.slice(othersIndex + 2);
                  const othersWithCustom = customValue ? `Others (${customValue})` : 'Others';
                  return [...beforeOthers, othersWithCustom, ...afterOthers].join(', ') || 'Select water source';
                } else {
                  return selectedValues.length > 0 ? selectedValues.join(', ') : 'Select water source';
                }
              })();

              return (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      className="h-fit w-full justify-between whitespace-normal break-words text-left font-normal focus:outline-none"
                    >
                      {displayText}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent
                    side="bottom"
                    align="start"
                    sideOffset={4}
                    className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                    avoidCollisions={false}
                  >
                    <div className="overflow-auto">
                      <ScrollArea>
                        {WATER_SOURCE.map((option) => (
                          <div
                            key={option.value}
                            onClick={() => toggleValue(option.value)}
                            className="flex cursor-pointer items-center space-x-2 rounded p-2 text-sm hover:bg-gray-50"
                          >
                            <Checkbox checked={selectedValues.includes(option.value)} className="mr-2" />
                            {option.label}
                          </div>
                        ))}
                      </ScrollArea>
                    </div>

                    {hasOthers && (
                      <Input
                        autoFocus
                        placeholder="Please specify"
                        className="mt-2"
                        value={customValue}
                        onChange={(e) => {
                          const input = e.target.value;
                          const updated = [...selectedValues];

                          if (othersIndex >= 0) {
                            updated[othersIndex + 1] = input;
                            onChange(updated);
                          }
                        }}
                      />
                    )}
                  </PopoverContent>
                </Popover>
              );
            }}
          />
        </FormField>
        <FormField name="fertilizerUsed" label="Fertilizer Used" errors={errors} required>
          <Controller
            control={control}
            name="fertilizerUsed"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className={cn(errors.fertilizerUsed && 'border-red-500')}>
                  <SelectValue placeholder="Select Fertilizer Type" />
                </SelectTrigger>
                <SelectContent>
                  {FERTILIZER_CHEMICAL_USED_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        <FormField name="pesticideUsed" label="Pesticide Used" errors={errors} required>
          <Controller
            control={control}
            name="pesticideUsed"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange}>
                <SelectTrigger className={cn(errors.pesticideUsed && 'border-red-500')}>
                  <SelectValue placeholder="Select Pesticide Type" />
                </SelectTrigger>
                <SelectContent>
                  {FERTILIZER_CHEMICAL_USED_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
        <FormField name="farmImplements" label="Farm Implements" errors={errors} required>
          <Controller
            control={control}
            name="farmImplements"
            render={({ field: { onChange, value } }) => {
              const selectedValues: string[] = Array.isArray(value) ? value : [];

              const toggleValue = (val: string) => {
                let newValue = [...selectedValues];

                if (val === 'Others') {
                  const hasOthers = newValue.includes('Others');
                  if (hasOthers) {
                    newValue = newValue.filter((v, i) => v !== 'Others' && i !== newValue.indexOf('Others') + 1);
                  } else {
                    newValue.push('Others', '');
                  }
                } else {
                  if (newValue.includes(val)) {
                    newValue = newValue.filter((v) => v !== val);
                  } else {
                    newValue.push(val);
                  }
                }

                onChange(newValue);
              };

              const hasOthers = selectedValues.includes('Others');
              const othersIndex = selectedValues.indexOf('Others');
              const customValue =
                othersIndex >= 0 && selectedValues.length > othersIndex + 1 ? selectedValues[othersIndex + 1] : '';

              const displayText = (() => {
                if (othersIndex >= 0) {
                  const beforeOthers = selectedValues.slice(0, othersIndex);
                  const afterOthers = selectedValues.slice(othersIndex + 2);
                  const othersWithCustom = customValue ? `Others (${customValue})` : 'Others';
                  return [...beforeOthers, othersWithCustom, ...afterOthers].join(', ') || 'Select farm implements';
                } else {
                  return selectedValues.length > 0 ? selectedValues.join(', ') : 'Select farm implements';
                }
              })();

              return (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="outline"
                      className="h-fit w-full justify-between whitespace-normal break-words text-left font-normal focus:outline-none"
                    >
                      {displayText}
                    </Button>
                  </PopoverTrigger>

                  <PopoverContent
                    side="bottom"
                    align="start"
                    sideOffset={4}
                    className="w-[var(--radix-popover-trigger-width)] p-2 font-dmSans"
                    avoidCollisions={false}
                  >
                    <div className="overflow-auto">
                      <ScrollArea>
                        {FARM_IMPLEMENTS.map((option) => (
                          <div
                            key={option.value}
                            onClick={() => toggleValue(option.value)}
                            className="flex cursor-pointer items-center space-x-2 rounded p-2 text-sm hover:bg-gray-50"
                          >
                            <Checkbox checked={selectedValues.includes(option.value)} className="mr-2" />
                            {option.label}
                          </div>
                        ))}
                      </ScrollArea>
                    </div>

                    {hasOthers && (
                      <Input
                        autoFocus
                        placeholder="Please specify"
                        className="mt-2"
                        value={customValue}
                        onChange={(e) => {
                          const input = e.target.value;
                          const updated = [...selectedValues];

                          if (othersIndex >= 0) {
                            updated[othersIndex + 1] = input;
                            onChange(updated);
                          }
                        }}
                      />
                    )}
                  </PopoverContent>
                </Popover>
              );
            }}
          />
        </FormField>
      </div>
    </div>
  );
};

export default FarmPractices;
