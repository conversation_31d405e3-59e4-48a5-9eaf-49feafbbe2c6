'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon, Plus, X } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import { Button } from '@/components/ui/button';
import { Input, InputPassword } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import { useGlobalState } from '@/lib/store';
import { cn, uniqueIndentifier } from '@/lib/utils';

import { PROFILE_TAB } from '../../constants';
import { FarmerUtilityEnum, GovernmentIdentificationEnum } from './Enums';

export default function IdentificationDocs() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    reset,
    setValue,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      password: '',
      password_confirmation: '',
      governmentIdentification:
        data.farmer.governmentIdentifications.length > 0
          ? data.farmer.governmentIdentifications.value.map((item) => ({
              governmentIdType: item.government_id_type,
              governmentIdNumber: item.government_id_number,
              upload: null,
            }))
          : [
              {
                governmentIdType: '',
                governmentIdNumber: '',
                upload: null,
              },
            ],
      farmerUtilities:
        data.farmer.farmerUtilities.length > 0
          ? data.farmer.farmerUtilities.value.map((item) => ({
              identifier: item.identifier,
              type: item.type,
              provider: item.provider,
              upload: null,
            }))
          : [
              {
                identifier: uniqueIndentifier(),
                type: '',
                provider: '',
                upload: null,
              },
            ],
      mobileDeviceBrand: data.farmer.farmerMobileDevice?.mobile_device_brand?.value || '',
      mobileDeviceModel: data.farmer.farmerMobileDevice?.mobile_device_model?.value || '',
    },
  });
  const governId = useFieldArray({
    name: 'governmentIdentification',
    control,
  });
  const farmerUtilities = useFieldArray({
    name: 'farmerUtilities',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      governmentIdentification: _data.governmentIdentification
        .filter((item: any) => item.governmentIdType)
        .map((item: any) => ({
          governmentIdNumber: item.governmentIdNumber,
          governmentIdType: item.governmentIdType,
        })),
      farmerUtilities: _data.farmerUtilities
        .filter((item: any) => item.type)
        .map((item: any) => ({
          identifier: item.identifier,
          type: item.type,
          provider: item.provider,
        })),
      userId: data.farmer.user_id.value,
    };

    _data.governmentIdentification.map((item) => {
      if (item.upload) {
        updatedData = {
          ...updatedData,
          [`governmentIdentification_${item.governmentIdNumber}`]: item.upload[0],
        };
      }
    });

    _data.farmerUtilities.map((item) => {
      if (item.upload) {
        updatedData = {
          ...updatedData,
          [`farmerUtilities_${item.identifier}`]: item.upload[0],
        };
      }
    });

    console.log('Identification Docs: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  return (
    <form id={PROFILE_TAB[2].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-4">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {governId.fields.map((field, index) => {
            const errorForField = errors?.governmentIdentification?.[index];
            const currentType = watch(`governmentIdentification.${index}.governmentIdType`);
            const govImg =
              data.farmer.governmentIdentifications.length < index + 1
                ? ''
                : data.farmer.governmentIdentifications[index].government_id_image.value;
            const govImgSplit = govImg?.split('/');
            const govName = govImgSplit ? govImgSplit[govImgSplit.length - 1] : null;

            return (
              <div
                key={field.id}
                className={cn('grid sm:grid-cols-2 2xl:grid-cols-3 items-start gap-4', index === 0 ? 'pt-0' : 'pt-7')}
              >
                {/* ID Type */}
                <FormField
                  name={`governmentIdentification.${index}.governmentIdType`}
                  label="Government Identification Type"
                  errors={errors?.governmentIdentification?.[index] || {}}
                  required={false}
                >
                  <Controller
                    control={control}
                    name={`governmentIdentification.${index}.governmentIdType` as const}
                    rules={{ required: false }}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.governmentIdType &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select ID Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(GovernmentIdentificationEnum).map((idType) => (
                              <SelectItem key={idType} value={idType}>
                                {idType}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </FormField>

                {/* ID Number */}
                <FormField
                  name={`governmentIdentification.${index}.governmentIdNumber`}
                  label="Government ID Number"
                  errors={errors?.governmentIdentification?.[index] || {}}
                  required={!!currentType}
                >
                  <Input
                    {...register(`governmentIdentification.${index}.governmentIdNumber` as const, {
                      required: currentType ? 'Government ID Number is required' : false,
                      maxLength:
                        currentType === GovernmentIdentificationEnum.RSBSA
                          ? {
                              value: 20,
                              message: `${GovernmentIdentificationEnum.RSBSA} ID must be 15-16 characters`,
                            }
                          : currentType === GovernmentIdentificationEnum.TIN
                            ? {
                                value: 15,
                                message: `${GovernmentIdentificationEnum.TIN} must be 9-12 digits`,
                              }
                            : undefined,
                      minLength:
                        currentType === GovernmentIdentificationEnum.RSBSA
                          ? {
                              value: 19,
                              message: `${GovernmentIdentificationEnum.RSBSA} ID must be 15-16 characters`,
                            }
                          : currentType === GovernmentIdentificationEnum.TIN
                            ? {
                                value: 11,
                                message: `${GovernmentIdentificationEnum.TIN} must be 9-12 digits`,
                              }
                            : undefined,
                      onChange: (e) => {
                        if (currentType === GovernmentIdentificationEnum.RSBSA) {
                          const alphanumeric = e.target.value.replace(/[^A-Za-z0-9]/g, '').slice(0, 16);
                          // Format as 00-000-00-000-000000
                          let formatted = '';
                          if (alphanumeric.length > 0) {
                            const parts = [
                              alphanumeric.slice(0, 2),
                              alphanumeric.slice(2, 5),
                              alphanumeric.slice(5, 7),
                              alphanumeric.slice(7, 10),
                              alphanumeric.slice(10, 16),
                            ].filter((part) => part.length > 0);
                            formatted = parts.join('-');
                          }
                          e.target.value = formatted;
                        } else if (currentType === GovernmentIdentificationEnum.TIN) {
                          const onlyDigits = e.target.value.replace(/\D/g, '').slice(0, 12);
                          // Format as 000-123-456-001
                          if (onlyDigits.length >= 10) {
                            e.target.value = `${onlyDigits.slice(0, 3)}-${onlyDigits.slice(3, 6)}-${onlyDigits.slice(6, 9)}-${onlyDigits.slice(9, 12)}`;
                          } else if (onlyDigits.length >= 7) {
                            e.target.value = `${onlyDigits.slice(0, 3)}-${onlyDigits.slice(3, 6)}-${onlyDigits.slice(6)}`;
                          } else if (onlyDigits.length >= 4) {
                            e.target.value = `${onlyDigits.slice(0, 3)}-${onlyDigits.slice(3)}`;
                          } else if (onlyDigits.length >= 1) {
                            e.target.value = onlyDigits;
                          } else {
                            e.target.value = '';
                          }
                        }
                      },
                    })}
                    inputMode={
                      currentType === GovernmentIdentificationEnum.RSBSA
                        ? 'text'
                        : currentType === GovernmentIdentificationEnum.TIN
                          ? 'numeric'
                          : undefined
                    }
                    className={cn(errorForField?.governmentIdNumber && 'border-red-500')}
                    placeholder="Enter ID Number"
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                </FormField>

                {/* Upload ID */}
                <FormField
                  name={`governmentIdentification.${index}.upload`}
                  label="Upload ID Image"
                  errors={errors?.governmentIdentification?.[index] || {}}
                >
                  <div className="flex items-center gap-4">
                    <Input
                      {...register(`governmentIdentification.${index}.upload` as const, {
                        required: false,
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.governmentIdNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="file"
                      placeholder="Upload ID"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    <div className={cn(index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => governId.remove(index)}
                        disabled={!gState.accountProfileIsEdit.value}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>

                  {govImg && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={govImg} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {govName}
                      </a>
                    </div>
                  )}
                </FormField>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              governId.append({
                governmentIdType: '',
                governmentIdNumber: '',
                upload: null,
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More ID info</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Utility Bill Details</div>
      <div className="mt-6 space-y-4">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {farmerUtilities.fields.map((field, index) => {
            const errorForField = errors?.farmerUtilities?.[index];
            const govImg =
              data.farmer.farmerUtilities.length < index + 1 ? '' : data.farmer.farmerUtilities[index].bill.value;
            const govImgSplit = govImg?.split('/');
            const govName = govImgSplit ? govImgSplit[govImgSplit.length - 1] : null;

            return (
              <div
                key={field.id}
                className={cn('grid sm:grid-cols-2 2xl:grid-cols-3 items-start gap-4', index === 0 ? 'pt-0' : 'pt-7')}
              >
                {/* Type */}
                <FormField
                  name={`farmerUtilities.${index}.type`}
                  label="Utility Type"
                  errors={errors?.farmerUtilities?.[index] || {}}
                  required={false}
                >
                  <Controller
                    control={control}
                    name={`farmerUtilities.${index}.type` as const}
                    rules={{ required: false }}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.type && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="e.g., Water, Electricity, Internet" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(FarmerUtilityEnum).map((idType) => (
                              <SelectItem key={idType} value={idType}>
                                {idType}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                </FormField>

                {/* Service Provider*/}
                <FormField
                  name={`farmerUtilities.${index}.provider`}
                  label="Service Provider"
                  errors={errors?.farmerUtilities?.[index] || {}}
                  required={watch(`farmerUtilities.${index}.type`) ? true : false}
                >
                  <Input
                    {...register(`farmerUtilities.${index}.provider` as const, {
                      required: watch(`farmerUtilities.${index}.type`) ? 'Service Provider is required' : false,
                    })}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.provider && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="e.g., Meralco, Maynilad, PLDT"
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                </FormField>

                {/* Upload */}
                <FormField
                  name={`farmerUtilities.${index}.upload`}
                  label="Upload Utility Bill"
                  errors={errors?.farmerUtilities?.[index] || {}}
                  required={watch(`farmerUtilities.${index}.type`) ? true : false}
                >
                  <div className="flex items-center gap-4">
                    <Input
                      {...register(`farmerUtilities.${index}.upload` as const, {
                        required: watch(`farmerUtilities.${index}.type`)
                          ? govImg
                            ? false
                            : 'Utility Bill is required'
                          : false,
                      })}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.upload && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="file"
                      placeholder="Upload Utility Bill"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    <div className={cn(index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => farmerUtilities.remove(index)}
                        disabled={!gState.accountProfileIsEdit.value}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>

                  {govImg && (
                    <div className="">
                      <a className="flex items-center gap-2 text-primary hover:underline" href={govImg} target="_blank">
                        <PaperclipIcon className="size-4" />
                        {govName}
                      </a>
                    </div>
                  )}
                </FormField>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              farmerUtilities.append({
                identifier: uniqueIndentifier(),
                type: '',
                provider: '',
                upload: null,
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Utility</span>
          </Button>
        </div>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Mobile Device Details</div>
      <div className="my-6 grid gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Mobile Device Brand */}
        <FormField name="mobileDeviceBrand" label="Mobile Device Brand" errors={errors}>
          <Input
            {...register('mobileDeviceBrand')}
            className={cn(
              'focus-visible:ring-primary',
              errors?.mobileDeviceBrand && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="e.g., Apple, Samsung, Huawei"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Mobile Device Model */}
        <FormField name="mobileDeviceModel" label="Mobile Device Model" errors={errors}>
          <Input
            {...register('mobileDeviceModel')}
            className={cn(
              'focus-visible:ring-primary',
              errors?.mobileDeviceModel && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="e.g., Iphone 14 Pro Max, Galaxy S21"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>
      </div>

      <div className="font-dmSans text-xl font-bold text-primary">Account Settings</div>
      <div className="mt-6 grid gap-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Password */}
        <FormField name="password" label="Password" errors={errors}>
          <InputPassword
            {...register('password', {
              required: false,
              validate: {
                min: (value) => (value ? `${value}`.length >= 8 || `Min. 8 characters` : true),
                regex: (v: string) =>
                  v
                    ? /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]).*$/.test(v) ||
                      `Password must contain alphanumeric characters (A-Z, a-z, 0-9) and at least one special character (!@#$%^&*...`
                    : true,
              },
            })}
            className={cn('focus-visible:ring-primary', errors.password && 'border-red-500 focus-visible:ring-red-500')}
            placeholder="Min. 8 characters"
            autoComplete="off"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Confirm Password */}
        <FormField name="password_confirmation" label="Re-Type Password" errors={errors}>
          <InputPassword
            {...register('password_confirmation', {
              required: false,
              validate: {
                isMatch: (value) => (value ? value === watch('password') || `Passwords don't match` : true),
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.password_confirmation && 'border-red-500 focus-visible:ring-red-500',
            )}
            placeholder="Min. 8 characters"
            autoComplete="off"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>
      </div>
    </form>
  );
}
