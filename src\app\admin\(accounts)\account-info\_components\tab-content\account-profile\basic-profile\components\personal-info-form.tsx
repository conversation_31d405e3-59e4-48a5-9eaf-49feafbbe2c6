'use client';

import { Control, Controller, FieldErrors, FieldValues, UseFormRegister } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { NATIONALITIES } from '@/lib/constants/nationality';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

interface IPersonalInfoFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
}

export default function PersonalInfoForm({ register, control, errors }: IPersonalInfoFormProps) {
  const gState = useGlobalState();

  return (
    <div>
      <FormTitle title="Personal Information" />

      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        {/* First Name */}
        <FormField name="firstName" label="First Name" errors={errors} required>
          <Input
            {...register('firstName', { required: 'First Name is required' })}
            className={cn(
              'focus-visible:ring-primary',
              errors.firstName && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter First Name"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Middle Name */}
        <FormField name="middleName" label="Middle Name" errors={errors} required>
          <Input
            {...register('middleName')}
            className={cn(
              'focus-visible:ring-primary',
              errors.middleName && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Middle Name"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Last Name */}
        <FormField name="lastName" label="Last Name" errors={errors} required>
          <Input
            {...register('lastName', { required: 'Last Name is required' })}
            className={cn('focus-visible:ring-primary', errors.lastName && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Last Name"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Birth Date */}
        <FormField name="birthDate" label="Birth Date" errors={errors} required>
          <Input
            {...register('birthDate', { required: 'Birth Date is required' })}
            className={cn(
              'focus-visible:ring-primary',
              errors.birthDate && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="date"
            placeholder="Enter Birth Date"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Place of Birth */}
        <FormField name="placeOfBirth" label="Place of Birth" errors={errors}>
          <Input
            {...register('placeOfBirth')}
            className={cn(
              'focus-visible:ring-primary',
              errors.placeOfBirth && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Place of Birth"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Gender */}
        <FormField name="gender" label="Gender" errors={errors}>
          <Controller
            control={control}
            name="gender"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.gender && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="MALE">Male</SelectItem>
                    <SelectItem value="FEMALE">Female</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {/* Religion */}
        <FormField name="religion" label="Religion" errors={errors}>
          <Input
            {...register('religion')}
            className={cn('focus-visible:ring-primary', errors.religion && 'border-red-500 focus-visible:ring-red-500')}
            type="text"
            placeholder="Enter Religion"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Nationality */}
        <FormField name="nationality" label="Nationality" errors={errors}>
          <Controller
            control={control}
            name="nationality"
            render={({ field: { onChange, value } }) => (
              <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.nationality && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select nationality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {NATIONALITIES.map((nationality) => (
                      <SelectItem key={nationality} value={nationality}>
                        {nationality}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>
    </div>
  );
}
