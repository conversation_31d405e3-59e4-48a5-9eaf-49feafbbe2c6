'use client';

import { useHookstate } from '@hookstate/core';
import { Calculator } from 'lucide-react';
import Link from 'next/link';
import { useEffect } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { useGlobalStatePersist } from '@/lib/store/persist';

export default function AgronomistDashboardPage() {
  const gStateP = useGlobalStatePersist();
  const activeMenu = useHookstate(gStateP.agronomist.activeMenu);

  // Set active menu to Dashboard (id: 0)
  useEffect(() => {
    activeMenu.set(0);
  }, []);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Agronomist Dashboard</h1>
        <p className="text-gray-500">Welcome to the Agronomist Dashboard</p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Link href="/agronomist/farm-plan">
          <Card className="cursor-pointer transition-all hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Farm Plan & Calculator</CardTitle>
              <Calculator className="size-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">Farm Plans</div>
              <p className="text-xs text-muted-foreground">Create and manage farm plans for farmers</p>
            </CardContent>
          </Card>
        </Link>
      </div>
    </div>
  );
}
