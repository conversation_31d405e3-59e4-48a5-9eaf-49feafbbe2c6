'use client';

import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

export default function TransactionDetails() {
  const router = useRouter();
  const gStateP = useGlobalStatePersist();
  const data = gStateP.admin.transaction['details'].get({ noproxy: true });
  const address = data && data['farmer']['address'] ? JSON.parse(data['farmer']['address']) : {};

  return (
    <div className="flex-1 p-8">
      <h1 className="text-2xl font-bold tracking-tight md:text-3xl">Transaction Details</h1>
      <Breadcrumb className="mt-2">
        <BreadcrumbList>
          <BreadcrumbItem className="cursor-pointer">
            <BreadcrumbLink onClick={() => router.back()}>Account Information</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Transaction Details</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {data && (
        <div className="mt-6 grid gap-6">
          {/* Account Information */}
          <div className="card rounded-[20px]">
            <div className="flex flex-col gap-8 md:flex-row">
              <div>
                {/* Profile Image */}
                <div>
                  <img
                    className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                    src={
                      data.farmer.user.user_img
                        ? urlify(data.farmer.user.user_img, 'users/profile')
                        : '/assets/user-default.jpg'
                    }
                    alt=""
                  />
                </div>
              </div>

              <div className="flex-1">
                <div className="text-center text-xl font-bold leading-loose text-indigo-900 md:text-left">
                  Account Information
                </div>
                <dl className="grid gap-4 xl:grid-cols-2">
                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data.farmer.first_name} ${data.farmer.last_name}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`ID${data.id.toString().padStart(9, '0')}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data.farmer.mobile_number ?? ''}`}
                      </dd>
                    </div>
                  </div>

                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                      <dd className="mt-1 line-clamp-2 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${new Date(data.farmer.birth_date).toLocaleString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.farmer.user.email}`}</dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          {/* Entry & Exit */}
          <div className="grid grid-cols-2 gap-4 lg:grid-cols-3 2xl:grid-cols-5">
            <div className="card space-y-2 rounded-[16px]">
              <div className="text-[#A3AED0]">Entry Date & Time</div>
              <div className="text-sm text-primary">
                {data.entry_time ? format(new Date(data.entry_time), 'MMMM dd, yyyy | hh:mm aaa') : 'N/A'}
              </div>
            </div>

            <div className="card space-y-2 rounded-[16px]">
              <div className="text-[#A3AED0]">Entry Weight (kg)</div>
              <div className="text-sm text-primary">{Number(data.entry_weight).toLocaleString()}</div>
            </div>

            <div className="card space-y-2 rounded-[16px]">
              <div className="text-[#A3AED0]">Exit Date & Time</div>
              <div className="text-sm text-primary">
                {data.exit_time ? format(new Date(data.exit_time), 'MMMM dd, yyyy | hh:mm aaa') : 'N/A'}
              </div>
            </div>

            <div className="card space-y-2 rounded-[16px]">
              <div className="text-[#A3AED0]">Exit Weight (kg)</div>
              <div className="text-sm text-primary">{Number(data.exit_weight).toLocaleString()}</div>
            </div>

            <div className="card space-y-2 rounded-[16px]">
              <div className="text-[#A3AED0]">Transaction Value</div>
              <div className="text-sm text-primary">
                {data.gross_sales.toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })}
              </div>
            </div>
          </div>

          {/* Crops */}
          <div className="card overflow-hidden border p-0">
            <Table className="table-fixed">
              <TableHeader>
                <TableRow>
                  <TableHead>Crops</TableHead>
                  <TableHead>Percentage</TableHead>
                  <TableHead>Estimated Weight (kg)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.crops.map((crop, index) => (
                  <TableRow
                    key={crop.id}
                    className={cn(index % 2 === 0 ? 'bg-slate-50' : 'bg-white', 'hover:bg-slate-100')}
                  >
                    <TableCell className="font-medium">{crop.crop.name}</TableCell>
                    <TableCell>{crop.percentage}%</TableCell>
                    <TableCell>{(Number(data.entry_weight ?? 0) * (crop.percentage / 100)).toLocaleString()}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
}
