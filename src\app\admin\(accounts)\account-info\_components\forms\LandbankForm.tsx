'use client';

import { Check } from 'lucide-react';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { urlify } from '@/lib/utils';

export default function LandbankForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Landbank Customer Information Sheet',
  });

  const address = farmerData && farmerData['farmer']['address'] ? JSON.parse(farmerData['farmer']['address']) : {};

  const addressPermanent =
    farmerData && farmerData['farmer']['permanent_address']
      ? JSON.parse(farmerData['farmer']['permanent_address'])
      : {};

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Landbank Customer Information Sheet</DialogTitle>
          </DialogHeader>
          <div className="mx-auto flex max-h-[50vh] max-w-4xl flex-1 justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div
              ref={contentRef}
              className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/landbankForm.jpg)] bg-contain bg-top bg-no-repeat capitalize"
            >
              {/* Profile Image */}
              <img
                className="absolute right-[17px] top-8 aspect-square h-[6.7rem] object-cover"
                src={farmerData?.user_img ? urlify(farmerData?.user_img, 'users/profile') : '/assets/user-default.jpg'}
                alt=""
              />

              {/* name */}
              <div className="absolute left-2 right-4 top-[180px] flex justify-between text-sm">
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.first_name || ' '}</div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.middle_name || ' '}</div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.last_name || ' '}</div>
              </div>

              {/* Address */}
              <div className="absolute left-2 right-4 top-[235px] flex justify-between text-xs">
                <div className="flex flex-1 justify-between px-2 text-xs">
                  <div className="flex text-xs">{address?.addressHouseNumber || ' '}</div>
                  <div className="flex text-xs">
                    {address?.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''}
                  </div>
                  <div className="flex text-xs">
                    {address?.addressCity ? JSON.parse(address.addressCity)?.city_name : ' '}
                  </div>
                  <div className="flex text-xs">
                    {address?.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ' '}
                  </div>
                </div>
                <div className="flex w-[36%]">
                  <div className="ml-6 flex flex-1 justify-center">{address?.addressZipCode || ' '}</div>
                  <div className="ml-6 flex flex-1 justify-center">Philippines</div>
                  <div className="ml-6 flex flex-1 justify-center">
                    {farmerData?.farmer?.address_length_of_stay || ' '}
                  </div>
                </div>
              </div>

              {/* Permanent Address */}
              <div className="absolute left-2 right-4 top-[290px] flex justify-between text-sm">
                <div className="flex flex-1 justify-between px-2 text-xs">
                  <div className="flex">{addressPermanent?.permanentAddressHouseNumber || ' '}</div>
                  <div className="flex">
                    {addressPermanent?.permanentAddressBarangay
                      ? JSON.parse(addressPermanent?.permanentAddressBarangay)?.brgy_name
                      : ''}
                  </div>
                  <div className="flex">
                    {addressPermanent?.permanentAddressCity
                      ? JSON.parse(addressPermanent?.permanentAddressCity)?.city_name
                      : ' '}
                  </div>
                  <div className="flex">
                    {addressPermanent?.permanentAddressProvince
                      ? JSON.parse(addressPermanent.permanentAddressProvince)?.province_name
                      : ' '}
                  </div>
                </div>
                <div className="flex w-[36%]">
                  <div className="ml-6 flex flex-1 justify-center">
                    {addressPermanent?.permanentAddressZipCode || ' '}
                  </div>
                  <div className="ml-6 flex flex-1 justify-center">Philippines</div>
                  <div className="ml-6 flex flex-1 justify-center">
                    {farmerData?.farmer?.permanent_address_length_of_stay || ' '}
                  </div>
                </div>
              </div>

              {/* Tin No */}
              <div className="absolute left-8 right-4 top-[385px] flex justify-between text-sm">
                {farmerData?.farmer?.governmentIdentifications?.find((id) => id?.government_id_type === 'TIN')
                  ?.government_id_number || ''}
              </div>

              {/* Contact Number */}
              <div className="absolute left-8 right-4 top-[425px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.mobile_number || ''}</div>
                <div className="ml-2 flex flex-1"></div>
                <div className="ml-2 flex flex-1 !lowercase">{farmerData?.email || ''}</div>
              </div>

              {/* Profession */}
              <div className="absolute left-2 top-[470px] flex text-sm">
                <div className="ml-2 flex">{farmerData?.farmer?.occupation_title || ''}</div>
              </div>

              {/* Source of Income */}

              <div className="absolute left-[180px] top-[460px] text-sm">
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('SALARY') ? 'left-1 top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('COMMISSION') ? 'left-1 top-5' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('BUSINESS') ? 'left-36 top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('PENSION') ? 'left-36 top-5' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('OFW') ? 'left-[265px] top-0' : 'hidden'
                  }`}
                />
                <Check
                  className={`absolute flex text-sm ${
                    farmerData?.farmer?.source_of_funds?.includes('OTHER') ? 'left-[265px] top-5' : 'hidden'
                  }`}
                />
              </div>

              {/* Birthplace */}
              <div className="absolute left-2 right-4 top-[530px] flex justify-between text-sm">
                <div className="ml-6 flex flex-1">
                  {farmerData?.farmer?.birth_date
                    ? new Date(farmerData?.farmer?.birth_date).toLocaleDateString('en-US', {
                        month: '2-digit',
                        day: '2-digit',
                        year: '2-digit',
                      })
                    : ''}
                </div>
                <div className="ml-6 flex flex-1">{farmerData?.farmer?.place_of_birth || ''}</div>
                <div className="ml-6 flex flex-1 justify-between">
                  <div className="flex flex-1">{farmerData?.farmer?.farmerInfo?.nationality || ''}</div>
                  <div className="flex flex-1">{farmerData?.farmer?.gender || ''}</div>
                </div>
              </div>

              {/* Civil Status */}
              <div
                className={`absolute right-4 top-[565px] flex justify-between text-sm ${
                  farmerData?.farmer?.civil_status === 'SINGLE'
                    ? 'left-[10px]'
                    : farmerData?.farmer?.civil_status === 'MARRIED'
                      ? 'left-[90px]'
                      : farmerData?.farmer?.civil_status === 'WIDOWED'
                        ? 'left-[253px]'
                        : farmerData?.farmer?.civil_status === 'DIVORCED'
                          ? 'left-[183px]'
                          : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Mother's Maiden Name */}
              <div className="absolute left-[400px] right-4 top-[570px] flex text-sm">
                <div className="ml-2 flex">{farmerData?.farmer?.mothers_maiden_name || ''}</div>
              </div>

              {/* Government IDs */}
              <div className="absolute inset-x-4 top-[605px] flex flex-col text-sm">
                {farmerData?.farmer?.governmentIdentifications.slice(0, 2).map((item: any) => (
                  <div className="ml-2 flex" key={item.identifier}>
                    {item?.government_id_type + ` (${item?.government_id_number})`}
                  </div>
                ))}
              </div>

              {/* Spouse Name */}
              <div className="absolute left-[400px] right-4 top-[620px] flex text-sm">
                <div className="ml-2 flex">
                  {farmerData?.farmer?.familyProfiles?.find((profile) => profile?.relationship === 'SPOUSE')?.name ||
                    ''}
                </div>
              </div>

              {/* Employer Name */}
              <div className="absolute inset-x-4 top-[680px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1 justify-center">
                  {farmerData?.farmer?.occupation_employer_name || ''}
                </div>
                <div className="ml-2 flex flex-1"></div>
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.occupation_title || ''}</div>
              </div>

              {/* Employer Address */}
              <div className="absolute left-8 right-4 top-[730px] flex justify-between text-sm">
                <div className="ml-2 flex flex-1">{farmerData?.farmer?.occupation_employer_address || ''}</div>
                <div className="ml-2 flex w-1/5">Philippines</div>
              </div>

              {/* Occupation */}
              <div
                className={`absolute right-4 flex justify-between text-sm ${
                  farmerData?.farmer?.occupation === 'GOVERNMENT EMPLOYEE' ||
                  farmerData?.farmer?.occupation === 'PRIVATE EMPLOYEE'
                    ? 'left-[12px] top-[760px]'
                    : farmerData?.farmer?.occupation === 'SELF EMPLOYED PROFESSIONAL' ||
                        farmerData?.farmer?.occupation === 'SELF EMPLOYED NONPROFESSIONAL'
                      ? 'left-[12px] top-[775px]'
                      : farmerData?.farmer?.occupation === 'OFW'
                        ? 'left-[145px] top-[760px]'
                        : farmerData?.farmer?.occupation === 'RETIREE'
                          ? 'left-[145px] top-[775px]'
                          : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Monthly Income */}
              <div
                className={`absolute right-4 flex justify-between text-sm ${
                  Number(farmerData?.farmer?.occupation_annual_income) <= 120000
                    ? 'left-[330px] top-[760px]'
                    : Number(farmerData?.farmer?.occupation_annual_income) <= 240000
                      ? 'left-[330px] top-[775px]'
                      : Number(farmerData?.farmer?.occupation_annual_income) <= 360000
                        ? 'left-[480px] top-[760px]'
                        : Number(farmerData?.farmer?.occupation_annual_income) <= 480000
                          ? 'left-[480px] top-[775px]'
                          : Number(farmerData?.farmer?.occupation_annual_income) <= 600000
                            ? 'right-[165px] top-[760px]'
                            : Number(farmerData?.farmer?.occupation_annual_income) > 600000
                              ? 'right-[165px] top-[775px]'
                              : 'hidden'
                }`}
              >
                <Check />
              </div>

              {/* Existing Account at Landbank */}
              <div className={`absolute inset-x-4 top-[818px] flex text-sm`}>
                {/* <div className="flex w-[40%] bg-red-300/10 h-11"> */}
                <div className="grid w-1/3 grid-cols-3 gap-x-14">
                  <Check
                    id="SA-ATM"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('SA-ATM') ? '' : 'invisible'}
                  />
                  <Check
                    id="TRUST"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('TRUST') ? '' : 'invisible'}
                  />
                  <Check
                    id="TRADE"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('TRADE') ? '' : 'invisible'}
                  />

                  <Check
                    id="CA-ATM"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('CA-ATM') ? '' : 'invisible'}
                  />
                  <Check
                    id="LOANS"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('LOANS') ? '' : 'invisible'}
                  />
                  <Check
                    id="CREDIT CARD"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('CREDIT CARD') ? '' : 'invisible'}
                  />

                  <Check
                    id="TIME DEPOSIT"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('TIME DEPOSIT') ? '' : 'invisible'}
                  />
                  <Check
                    id="TREASURY"
                    size={17}
                    className={farmerData?.farmer?.landbank_accounts?.includes('TREASURY') ? '' : 'invisible'}
                  />
                </div>

                <div className="ml-14 flex flex-1 flex-col">
                  {farmerData?.farmer?.farmerBankDetails.slice(0, 3).map((i) => (
                    <div key={i.bank_account_number} className="flex flex-1 justify-between leading-none">
                      <div className="flex flex-1 justify-center text-xs">{i.bank_name}</div>
                      <div className="flex flex-1 justify-center text-xs">{i.bank_account_type}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
