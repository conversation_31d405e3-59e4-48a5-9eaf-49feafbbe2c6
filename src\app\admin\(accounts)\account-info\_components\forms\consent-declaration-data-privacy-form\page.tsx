'use client';

import { Square, SquareCheckBig } from 'lucide-react';
import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { FormDimensions } from '@/lib/constants/enums';

export default function ConsentDeclarationDataPrivacyForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Consent Declaration and Data Privacy Policy Statement',
  });

  console.log('Farmer: ', farmerData);

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Consent Declaration and Data Privacy Policy Statement</DialogTitle>
          </DialogHeader>
          <div className="mx-auto max-h-[50vh] max-w-4xl flex-1 flex-row justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef} className="font-poppins">
              <div className={`relative flex flex-col border bg-white p-20 print:border-none ${FormDimensions.LEGAL}`}>
                {/* Header with Logo */}
                <div className="mb-6 flex justify-end">
                  <img src="/kita-logo.png" alt="Kita Logo" className="h-12" />
                </div>

                {/* Title */}
                <h1 className="mb-6 text-center text-lg font-bold text-blue-800">
                  Consent Declaration and Data Privacy Policy Statement
                </h1>

                {/* Content */}
                <div className="space-y-4 text-justify text-sm">
                  <li>
                    Sumasang-ayon ako na maaaring gamitin ng KITA AGRITECH CORPORATION (&quot;KITA AGRITECH&quot;) ang
                    impormasyong ito para sa pagpaparehistro ko sa Farmer Kita Program, alinsunod sa Data Privacy Policy
                    Statement na nasa ibaba.
                  </li>
                  <li>
                    Pinapayagan ko ang KITA AGRITECH na bumisita sa aking sakahan at ako ay magbibigay ng karagdagang
                    mga dokumento, at pahintulot sa pagkuha ng mga larawan at bidyo, upang beripikahin ang impormasyong
                    aking inilagay sa form na ito.
                  </li>
                  <li>
                    Pinapayagan ko ang KITA AGRITECH na ibahagi ang mga impormasyong aking ibinigay sa mga Partner
                    Financial Institutions nito kung sakali man na ako ay mag-apply para sa Farm Production Loan bilang
                    bahagi ng KYC procedures nito.
                  </li>
                  <li>
                    Pinahihintulutan ko ang Kita Agritech Corp. na magsagawa ng reference checks at background
                    verification bilang bahagi ng proseso.
                  </li>
                  <li>
                    Pinapayagan ko ang Kita Agritech Corp. na gamitin ang aking personal na impormasyon para sa layunin
                    ng pagpapadala ng mga marketing materials, promotional content, at iba pang anunsyo na may kaugnayan
                    sa kanilang mga produkto at serbisyo.
                  </li>
                  <li>
                    Pinatutunayan ko na ang lahat ng impormasyong aking isinulat sa application na ito ay totoo at
                    kumpleto ayon sa aking kaalaman. Nauunawaan ko na ang anumang maling o mapanlinlang na impormasyon
                    ay maaaring magresulta sa pagkadiskwalipika sa aking application.
                  </li>
                  <li>
                    Nauunawaan ko na maaari kong bawiin ang aking pahintulot sa paggamit ng aking personal na datos
                    anumang oras sa pamamagitan ng pormal na pakikipag-ugnayan sa Kita Agritech Corp.
                  </li>
                </div>

                {/* Checkbox */}
                <div className="mt-6 flex items-start">
                  {farmerData?.farmer?.farmerDataPrivacy &&
                  farmerData?.farmer?.farmerDataPrivacy?.is_agree_using_data === 1 ? (
                    <SquareCheckBig className="mr-2 size-7" />
                  ) : (
                    <Square className="mr-2 size-7" />
                  )}
                  {/* <SquareCheckBig className="mr-2 size-7" />
                  <Square className="mr-2 size-7" /> */}
                  <p className="text-sm">
                    Nabasa at naintindihan ko ang mga Tuntunin at Kundisyon ng prosesong ito ng aplikasyon at ang Data
                    Privacy Statement, at ako ay sumasang-ayon dito.
                  </p>
                </div>

                {/* Signature Section */}
                <div className="mt-10 flex w-fit flex-col text-sm">
                  {farmerData?.farmer?.farmerDataPrivacy?.signature && (
                    <img
                      className="h-[50px] w-[255px] object-contain"
                      src={farmerData?.farmer?.farmerDataPrivacy?.signature}
                      alt="farmer signature"
                    />
                  )}
                  <div className="mb-1 w-64 border-b border-black text-center capitalize">
                    {`${farmerData?.farmer?.first_name || ''} ${farmerData?.farmer?.middle_name || ''} ${farmerData?.farmer?.last_name || ''}`.trim()}
                  </div>
                  <div className="text-center text-sm">Signature over printed name</div>

                  <div className="text-center text-sm">
                    {farmerData?.farmer?.farmerDataPrivacy &&
                      new Date(farmerData?.farmer?.farmerDataPrivacy?.updated_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
