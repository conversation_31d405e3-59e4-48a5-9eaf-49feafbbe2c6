'use client';

import { useQuery } from '@tanstack/react-query';

import { fetchLoanRequirements, fetchLoanRequirementsDetails } from '../api/loan-requirements.api';

export const useLoanRequirements = (user_id: number | string, currentUser = 'admin') => {
  const loanRequirementsQuery = useQuery({
    queryKey: [currentUser, 'loanRequirements', user_id],
    queryFn: () => fetchLoanRequirements(user_id, currentUser),
    enabled: !!user_id,
  });

  return {
    loanRequirementsQuery,
    loanRequirements: loanRequirementsQuery.data,
    isLoading: loanRequirementsQuery.isLoading || loanRequirementsQuery.isFetching,
    isError: loanRequirementsQuery.isError,
    error: loanRequirementsQuery.error,
  };
};

export const useViewDetailsLoanRequirement = (user_id: number | string, stage = 1, currentUser = 'admin') => {
  const viewDetailsLoanRequirementQuery = useQuery({
    queryKey: [currentUser, 'viewDetailsLoanRequirement', user_id, stage],
    queryFn: () => fetchLoanRequirementsDetails(user_id, stage, currentUser),
    enabled: !!user_id,
  });

  return {
    viewDetailsLoanRequirementQuery,
    viewDetailsLoanRequirement: viewDetailsLoanRequirementQuery.data,
    isLoading: viewDetailsLoanRequirementQuery.isLoading || viewDetailsLoanRequirementQuery.isFetching,
    isError: viewDetailsLoanRequirementQuery.isError,
    error: viewDetailsLoanRequirementQuery.error,
  };
};
