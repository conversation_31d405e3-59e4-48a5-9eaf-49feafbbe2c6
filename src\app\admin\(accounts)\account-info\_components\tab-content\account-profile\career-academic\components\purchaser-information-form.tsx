'use client';

import { Control, Controller, FieldErrors, FieldValues, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

// Import constants from FRO
const YES_NO_OPTIONS = [
  { label: 'Yes', value: '1' },
  { label: 'No', value: '0' },
];

interface IPurchaserInformationFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
}

export default function PurchaserInformationForm({ register, control, errors }: IPurchaserInformationFormProps) {
  const gState = useGlobalState();

  return (
    <div>
      <FormTitle title="Purchaser Information" />
      <div className="mt-6 grid gap-4 space-y-4 sm:grid-cols-2 xl:grid-cols-3">
        {/* Selling Location for Harvested Crops */}
        <FormField name="purchaserSellingLocation" label="Selling Location for Harvested Crops" errors={errors}>
          <Input
            {...register('purchaserSellingLocation')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserSellingLocation && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Selling Location for Harvested Crops"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Buyer's Full Name */}
        <FormField name="purchaserFullname" label="Buyer's Full Name" errors={errors}>
          <Input
            {...register('purchaserFullname')}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserFullname && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Full Name"
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Buyer's Contact No. */}
        <FormField name="purchaserContactNumber" label="Buyer's Contact No." errors={errors}>
          <Input
            {...register('purchaserContactNumber', {
              required: false,
              validate: {
                isValidContactNumber: (v) =>
                  v
                    ? /^(\+\d{1,3}\s)?((\+639)|0?9)\d{2}\s?\d{3}\s?\d{4}$/.test(v || '') ||
                      'Invalid contact number format (e.g. ***********)'
                    : true,
              },
            })}
            className={cn(
              'focus-visible:ring-primary',
              errors.purchaserContactNumber && 'border-red-500 focus-visible:ring-red-500',
            )}
            type="text"
            placeholder="Enter Buyer's Contact No."
            disabled={!gState.accountProfileIsEdit.value}
          />
        </FormField>

        {/* Interested to Sell at Trading Post */}
        <FormField name="isInterestedToSellAtTradingPost" label="Interested to Sell at Trading Post?" errors={errors}>
          <Controller
            control={control}
            name="isInterestedToSellAtTradingPost"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.isInterestedToSellAtTradingPost &&
                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>
    </div>
  );
}
