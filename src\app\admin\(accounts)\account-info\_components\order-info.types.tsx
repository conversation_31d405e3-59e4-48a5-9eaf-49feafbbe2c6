export interface IOrderInfo {
  id: number;
  customer_id: number;
  payment_method: number;
  reference_number: string;
  total_price: number;
  order_status: number;
  payment_status: number;
  shipping_fee: number;
  shipping_address: string;
  shipping_date: string;
  fulfillment_type: number;
  created_at: string;
  updated_at: string;
  wallet_allocation: number;
  wallet_balance_before: number;
  wallet_balance_after: number;
  customer: ICustomer;
  marketplaceProductOrders: IMarketplaceProductOrders[];
  statusHistory: IStatusHistory[];
}

export interface ICustomer {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: string;
  remember_me_token: null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null;
  farmer: IFarmer;
}

export interface IFarmer {
  id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  mobile_number: string;
  address: string;
}

export interface IMarketplaceProductOrders {
  id: number;
  marketplace_order_id: number;
  marketplace_product_id: number;
  price: number;
  quantity: number;
  vatable: string;
  created_at: string;
  updated_at: string;
  marketplaceProduct: IMarketplaceProduct;
}

export interface IMarketplaceProduct {
  id: number;
  product_type: number;
  price: number;
  vatable: string;
  stocks: number;
  stocks_warning: number;
  code: string;
  weight: number;
  unit: string;
  image: string;
  description: string;
  seed_id: null;
  crop_id: number;
  fertilizer_id: null;
  chemical_id: null;
  status: number;
  created_at: string;
  updated_at: string;
  other_product_id: null;
  otherProduct: null;
  fertilizer: null;
  crop: Crop;
  chemical: null;
  seed: null;
}

export interface Crop {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
  harvest_days: number;
  is_sync: number;
  image: string;
  keywords: null;
}

export interface IStatusHistory {
  id: number;
  marketplace_order_id: number;
  status_type: number;
  message: string;
  created_at: string;
  updated_at: string;
  processed_by_id: number;
  processedBy: IProcessedBy;
}

export interface IProcessedBy {
  id: number;
  email: string;
  username: string;
  status: number;
  user_type: number;
  user_img: null;
  remember_me_token: null;
  created_at: string;
  updated_at: string;
  is_sync: number;
  rfid_number: null;
  sale: null;
  admin: IAdmin;
}

export interface IAdmin {
  id: number;
  first_name: string;
  last_name: string;
  user_id: number;
  created_at: string;
  updated_at: string;
}
