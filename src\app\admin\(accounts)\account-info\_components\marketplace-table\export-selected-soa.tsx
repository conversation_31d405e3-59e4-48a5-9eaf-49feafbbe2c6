'use client';

import { useHookstate } from '@hookstate/core';
import { PDFDownloadLink, PDFViewer } from '@react-pdf/renderer';
import { format } from 'date-fns';
import { useEffect } from 'react';
import { MdOutlineFileDownload } from 'react-icons/md';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { useSoaStore } from '@/lib/store/soa-store';
import { cn } from '@/lib/utils';

import SoaPdf from './soa-pdf';

export default function ExportSelectedSOA({ table }) {
  const { getSoaBulk, soaBulk } = useSoaStore();
  const dialogPreview = useHookstate(false);

  useEffect(() => {
    if (table.getFilteredSelectedRowModel().rows.length === 0) {
      soaBulk.set([]);
    } else {
      getSoaBulk(table.getFilteredSelectedRowModel().rows.map((row: any) => row.original.id));
    }
  }, [table.getFilteredSelectedRowModel().rows]);

  return (
    <div>
      <Button className="h-8" disabled={soaBulk.length === 0} onClick={() => dialogPreview.set(true)}>
        Export Soa
      </Button>

      <Dialog open={dialogPreview.value} onOpenChange={dialogPreview.set}>
        <DialogContent isXEnable={false} className="relative overflow-hidden !p-0 font-sans sm:max-w-screen-lg">
          <DialogHeader className="hidden">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
          </DialogHeader>

          {soaBulk.length > 0 && table.getFilteredSelectedRowModel().rows.length > 0 && (
            <PDFViewer className="h-[90vh] w-full">
              <SoaPdf soaBulk={soaBulk} />
            </PDFViewer>
          )}

          <div className="absolute right-[87px] top-px h-[54px] w-auto">
            <PDFDownloadLink
              document={<SoaPdf soaBulk={soaBulk} />}
              fileName={`SOA_${soaBulk.value[0]?.formatted?.farmerName.replaceAll(' ', '')}_${format(new Date(), 'yyyy-MM-dd')}`}
            >
              {({ blob, url, loading, error }) => (
                <div
                  className={cn('grid size-full place-items-center', {
                    invisible: loading,
                  })}
                >
                  <div className="grid size-8 place-items-center rounded-full bg-[#323639]  hover:bg-[#424649]">
                    <MdOutlineFileDownload className="size-5 text-white" />
                  </div>
                </div>
              )}
            </PDFDownloadLink>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
