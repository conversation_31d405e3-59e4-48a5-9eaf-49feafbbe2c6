import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { farmInformationSchema, TFarmInformationSchema } from '@/app/field-relation-officer/schemas';
import { IFarmer, IFarmInformation, ISafeParseResult } from '@/app/field-relation-officer/types';
import useFarmer from '@/lib/hooks/fro/useFarmer';

import FarmLocation from './FarmLocation';
import FarmPractices from './FarmPractices';

interface IFarmInformationProps {
  data: IFarmer;
}

const FarmInformation = ({ data }: IFarmInformationProps) => {
  const farmerInfo = data?.farmerInfo;
  const { updateFarmer } = useFarmer();
  const {
    register,
    control,
    formState: { errors, isSubmitting },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TFarmInformationSchema>({
    resolver: zodResolver(farmInformationSchema),
    defaultValues: {
      // Farm Location
      farmAddressHouseNumber: farmerInfo?.farm_address_house_number || '',
      farmAddressStreet: farmerInfo?.farm_address_house_number || '',
      farmAddressRegion: '',
      farmAddressProvince: farmerInfo?.farm_address_province || '',
      farmAddressCity: farmerInfo?.farm_address_city || '',
      farmAddressBarangay: farmerInfo?.farm_address_barangay || '',
      farmAddressZipCode: farmerInfo?.farm_address_zip_code || '',
      farmArea: farmerInfo?.farm_area?.toString() || '',
      farmOwnership: farmerInfo?.farm_ownership?.toString() || '',
      otherFarmOwnership: farmerInfo?.other_farm_ownership || '',
      cropsPlanted: data?.cropsPlanted.map(({ crop }) => `${crop.id}-${crop.name}`)?.slice() || [],

      // Farm Practices
      averageYieldPerYear: farmerInfo?.average_yield_per_year || '',
      waterSource: farmerInfo.water_source ? farmerInfo.water_source.split(',').map((v) => v.trim()) : ([] as any),
      fertilizerUsed: farmerInfo.fertilizer_used?.toString() || '',
      pesticideUsed: farmerInfo.pesticide_used?.toString() || '',
      farmImplements: farmerInfo.farm_implements
        ? farmerInfo.farm_implements.split(',').map((v) => v.trim())
        : ([] as any),
    },
  });

  const handleNext = handleSubmit(async () => {
    const { data, success } = farmInformationSchema.safeParse(values()) as ISafeParseResult<IFarmInformation>;

    if (success) {
      // Handle "Others" fields by replacing "Others" with the actual value
      const processFieldWithOthers = (fieldValue: string, othersValue: string) => {
        if (!fieldValue) return '';
        return fieldValue
          .split(',')
          .map((item) => {
            const trimmedItem = item.trim();
            return trimmedItem === 'Others' && othersValue ? othersValue : trimmedItem;
          })
          .join(', ');
      };

      const processedData = {
        ...data,
        waterSource: processFieldWithOthers(data.waterSource, data.waterSourceOthers),
        farmImplements: processFieldWithOthers(data.farmImplements, data.farmImplementsOthers),
        cropsPlanted: data.cropsPlanted.map((item) => item.split('-')[0].trim()),
      };

      await updateFarmer(processedData);
      console.log('FarmPractices:', processedData);
    }
  });

  return (
    <form onSubmit={handleNext}>
      <FarmLocation
        register={register}
        control={control}
        errors={errors}
        values={values}
        watch={watch}
        setValue={setValue}
      />
      <FarmPractices control={control} errors={errors} register={register} />

      <div className="mt-16 flex justify-end gap-4">
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Saving' : 'Save'}
        </Button>
      </div>
    </form>
  );
};

export default FarmInformation;
