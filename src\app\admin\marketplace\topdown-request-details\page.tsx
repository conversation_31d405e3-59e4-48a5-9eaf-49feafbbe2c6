'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { Suspense } from 'react';

import HorizontalScrollBar from '@/components/HorizontalScrollBar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

import { RequestStatus, RequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { getUserType } from '@/lib/constants';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { topDownState } from '@/lib/store/top-down-store';
import { cn, urlify } from '@/lib/utils';

import FetchDetails from '../_components/ewallet/top-down/FetchDetails';

export default function TopdownRequestDetailsPage() {
  const gStateP = useGlobalStatePersist();
  const data = useHookstate(topDownState.reqDetails);

  const address =
    data.value && data['user']['farmer']['address'].value ? JSON.parse(data['user']['farmer']['address'].value) : {};
  const infoTab = useHookstate(0);
  const router = useRouter();
  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';

  return (
    <div className="p-6 md:p-8">
      <Suspense>
        <FetchDetails />
      </Suspense>

      {data.value && (
        <div className="">
          {/* {gStateP['user'].value && isAdmin && data['status'].value == RequestStatus.PENDING && (
            <div className="mb-6 flex items-center justify-end gap-3">
              <TopupActionDialog action="approve" />
              <TopupActionDialog action="reject" />
            </div>
          )} */}

          <div className="card">
            <div className="flex flex-col gap-8 xl:flex-row">
              <div>
                {/* Profile Image */}
                <div>
                  <img
                    className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                    src={
                      data['user'].user_img.value
                        ? urlify(data['user'].user_img.value, 'users/profile')
                        : '/assets/user-default.jpg'
                    }
                    alt=""
                  />
                </div>

                <div className="mt-4 flex justify-center">
                  <Button
                    onClick={() => {
                      router.push(`/admin/account-info/?id=${data['user'].id.value}`);
                    }}
                  >
                    View more info
                  </Button>
                </div>
              </div>

              <div className="flex-1">
                <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
                <dl className="grid gap-4 md:grid-cols-2">
                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data['user'].farmer.first_name.value} ${data['user'].farmer.last_name.value}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`ID${data['user'].id.value.toString().padStart(9, '0')}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data['user'].farmer.mobile_number.value ?? ''}`}
                      </dd>
                    </div>
                  </div>

                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${new Date(data['user'].farmer.birth_date.value).toLocaleString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data['user'].email.value}`}</dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          <div className="card mt-6">
            {/* Tabs */}
            <HorizontalScrollBar>
              <div className="mr-4 flex min-w-max items-center gap-8">
                <button
                  className={cn(
                    'transition-all duration-300 ease-in-out',
                    infoTab.value === 0
                      ? 'font-bold text-primary/90 underline underline-offset-8'
                      : 'text-gray-500 hover:font-bold hover:text-primary/80',
                  )}
                  onClick={() => infoTab.set(0)}
                >
                  Request Details
                </button>
                <button
                  className={cn(
                    'transition-all duration-300 ease-in-out',
                    infoTab.value === 1
                      ? 'font-bold text-primary/90 underline underline-offset-8'
                      : 'text-gray-500 hover:font-bold hover:text-primary/80',
                  )}
                  onClick={() => infoTab.set(1)}
                >
                  Attachments
                </button>
                <button
                  className={cn(
                    'transition-all duration-300 ease-in-out',
                    infoTab.value === 2
                      ? 'font-bold text-primary/90 underline underline-offset-8'
                      : 'text-gray-500 hover:font-bold hover:text-primary/80',
                  )}
                  onClick={() => infoTab.set(2)}
                >
                  Remarks
                </button>
              </div>
            </HorizontalScrollBar>

            {/* Request Details */}
            {infoTab.value === 0 && (
              <dl className="mt-8 grid gap-4 md:grid-cols-2">
                <div className="font-dmSans">
                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Requested</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {format(new Date(data['created_at'].value), 'MMMM dd, yyyy | hh:mm a')}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Amount</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {data['amount'].value
                        ? Number(data['amount'].value).toLocaleString('en-US', {
                            style: 'currency',
                            currency: 'PHP',
                          })
                        : '-'}
                    </dd>
                  </div>

                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Requested By</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      {data['processedBy']['admin'].value
                        ? `${data['processedBy']['admin']['first_name'].value} ${data['processedBy']['admin']['last_name'].value}`
                        : data['processedBy']['finance'].value
                          ? `${data['processedBy']['finance']['first_name'].value} ${data['processedBy']['finance']['last_name'].value}`
                          : `-`}
                    </dd>
                  </div>
                </div>

                {/* Right */}
                <div className="font-dmSans">
                  <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-slate-400">Status</dt>
                    <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                      <Badge className={cn(RequestStatusLabels[data['status'].value].color, 'capitalize')}>
                        {RequestStatusLabels[data['status'].value].label}
                      </Badge>
                    </dd>
                  </div>

                  {/* FOR APPROVAL / PENDING */}
                  {(data['status'].value === RequestStatus.PENDING ||
                    data['status'].value === RequestStatus.APPROVE) && (
                    <>
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Approved</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {format(new Date(data['updated_at'].value), 'MMMM dd, yyyy | hh:mm a')}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Approved By</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${data['approvedBy'].value ? (data['approvedBy']['finance'].value ? `${data['approvedBy']['finance']['first_name'].value} ${data['approvedBy']['finance']['last_name'].value}` : data['approvedBy']['admin'].value ? `${data['approvedBy']['admin']['first_name'].value} ${data['approvedBy']['admin']['last_name'].value}` : `${data['approvedBy']['email'].value}`) : '-'}`}
                        </dd>
                      </div>
                    </>
                  )}

                  {/* REJECTED */}
                  {data['status'].value === RequestStatus.REJECTED && (
                    <>
                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Date & Time Rejected</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${
                            data['approvedBy'].value
                              ? new Date(data['approvedBy']['updated_at'].value).toLocaleString('en-US', {
                                  year: 'numeric',
                                  month: 'long',
                                  day: 'numeric',
                                  hour: 'numeric',
                                  minute: 'numeric',
                                  hour12: true,
                                })
                              : '-'
                          }`}
                        </dd>
                      </div>

                      <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                        <dt className="text-sm font-medium leading-6 text-slate-400">Rejected By</dt>
                        <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                          {`${data['approvedBy'].value ? (data['approvedBy']['finance'].value ? `${data['approvedBy']['finance']['first_name'].value} ${data['approvedBy']['finance']['last_name'].value}` : data['approvedBy']['admin'].value ? `${data['approvedBy']['admin']['first_name'].value} ${data['approvedBy']['admin']['last_name'].value}` : `${data['approvedBy']['email'].value}`) : '-'}`}
                        </dd>
                      </div>
                    </>
                  )}
                </div>
              </dl>
            )}

            {/* Attachments */}
            {infoTab.value === 1 && (
              <dl className="mt-8 grid-cols-2 gap-4 md:grid">
                <div className="font-dmSans">
                  {data['documents'].length > 0 &&
                    data['documents'].value.map((doc, index) => {
                      const split = doc.document.split('/');
                      const docLength = split.length;
                      const last = split[docLength - 1];

                      return (
                        <div key={doc.id} className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                          <dt className="text-sm font-medium leading-6 text-slate-400">{`Document ${index + 1}`}</dt>
                          <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                            <a href={doc.document} target="_blank" rel="noreferrer" className="text-blue-500 underline">
                              {last}
                            </a>
                          </dd>
                        </div>
                      );
                    })}
                </div>
              </dl>
            )}

            {/* Remarks */}
            {infoTab.value === 2 && (
              <div className="space-y-8 divide-y-2 divide-dashed">
                {data['remarks'].value.map((remark, index) => (
                  <div key={remark.id} className="pt-8">
                    <div>{remark.remarks}</div>

                    <div className="mt-4 flex gap-2 text-sm text-gray-500">
                      <span>
                        {remark.remarksBy.finance
                          ? `${remark.remarksBy.finance.first_name} ${remark.remarksBy.finance.last_name}`
                          : remark.remarksBy.admin
                            ? `${remark.remarksBy.admin.first_name} ${remark.remarksBy.admin.last_name}`
                            : `${remark.remarksBy.username}`}
                      </span>
                      <span>|</span>
                      <span>
                        {new Date(remark.created_at).toLocaleString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </span>
                      <span>|</span>
                      <span>
                        {new Date(remark.created_at).toLocaleString('en-US', {
                          hour: 'numeric',
                          minute: 'numeric',
                          hour12: true,
                        })}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
