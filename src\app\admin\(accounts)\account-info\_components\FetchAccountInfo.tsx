'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import { DEFAULT_ADMIN_ACTIVE_MENU, getUserType } from '@/lib/constants';
import useCreditScoring from '@/lib/hooks/admin/useCreditScoring';
import useLoanApplication from '@/lib/hooks/admin/useLoanApplication';
import useFinance from '@/lib/hooks/useFinance';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function FetchAccountInfo() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();

  const detailsId = useSearchParams().get('id');
  const { getAccountInfo, getFinance, getMarketplace, getTradingPost, getSalesTransaction, getLoanPayments } =
    useFinance();
  const { getCreditScoreByFarmer } = useCreditScoring();
  const { getLatest } = useLoanApplication();

  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';
  const isFinance = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'finance';

  useEffect(() => {
    if (detailsId) {
      getAccountInfo(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });

      if (isFinance) {
        router.push('/finance');
      } else if (isAdmin) {
        router.push('/admin');
        gStateP.admin.activeMenu.set(DEFAULT_ADMIN_ACTIVE_MENU);
      }
    }
  }, [detailsId]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      Promise.all([
        getCreditScoreByFarmer(gState.selected.accountInfo['info']['farmer']['id'].value),
        getLatest(gState.selected.accountInfo['info']['id'].value),
      ]);
      console.log('getCreditScoreByFarmer Fetch Account Info');
    }
  }, [gState.selected.accountInfo['info']]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      getTradingPost(detailsId);
      getSalesTransaction(detailsId);
    }
  }, [
    gState.selected.accountInfo.pagination.tradingPost.page,
    gState.selected.accountInfo.pagination.tradingPost.pageSize,
  ]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      getMarketplace(detailsId);
    }
  }, [
    gState.selected.accountInfo.pagination.marketplace.page,
    gState.selected.accountInfo.pagination.marketplace.pageSize,
  ]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      getFinance(detailsId);
    }
  }, [gState.selected.accountInfo.pagination.finance.page, gState.selected.accountInfo.pagination.finance.pageSize]);

  useEffect(() => {
    if (gState.selected.accountInfo['info'].value) {
      getLoanPayments(detailsId);
    }
  }, [
    gState.selected.accountInfo.pagination.loanPayment.page,
    gState.selected.accountInfo.pagination.loanPayment.pageSize,
  ]);

  return null;
}
