import axios from '@/lib/api';

import { ILoanRequirementsResponse, IViewDetailsLoanRequirementResponse } from '../types/loan-requirements.types';

export const fetchLoanRequirements = async (user_id: number | string, currentUser = 'admin') => {
  const res = await axios.get(`/${currentUser}/user/farmers/${user_id}/loanrequirement/view`);
  return res.data as ILoanRequirementsResponse;
};

export const fetchLoanRequirementsDetails = async (user_id: number | string, stage = 1, currentUser = 'admin') => {
  const res = await axios.get(`/${currentUser}/user/farmers/${user_id}/loanrequirement/stage/${stage}/view`);
  return res.data as IViewDetailsLoanRequirementResponse;
};
