'use client';

import { format } from 'date-fns';
import { useRouter } from 'next/navigation';
import { parseAsInteger, useQueryState } from 'nuqs';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';

import { LoanRequirementDetailsTable } from '../_components/tab-content/account-profile/loan-requirements/components/loan-requirement-details-table';
import { columnLoanRequirementDetails } from '../_components/tab-content/account-profile/loan-requirements/components/loan-requirement-details-table/columns';
import { useViewDetailsLoanRequirement } from '../_components/tab-content/account-profile/loan-requirements/hooks/useLoanRequirements';
import { LOAN_REQUIREMENTS_STAGE } from '../_components/tab-content/account-profile/loan-requirements/types/loan-requirements.types';

export default function LoanRequirementsDetailsPage() {
  const router = useRouter();

  const [userId] = useQueryState('id', parseAsInteger.withDefault(0));
  const [stage] = useQueryState('stage', parseAsInteger.withDefault(1));
  const loanReqsDetails = useViewDetailsLoanRequirement(userId, stage);
  const details = loanReqsDetails.viewDetailsLoanRequirement?.data[0];

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="">
        <h1 className="text-2xl font-bold tracking-tight md:text-3xl">{LOAN_REQUIREMENTS_STAGE[stage]}</h1>
        <Breadcrumb className="mt-2">
          <BreadcrumbList>
            <BreadcrumbItem className="cursor-pointer">
              <BreadcrumbLink onClick={() => router.back()}>Loan Requirements</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Details</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* Start Date & Download File */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-kitaph-primary">
            {details && details.stage_started_at
              ? `Start Date at ${format(new Date(details.stage_started_at), 'MMM dd, yyyy')}`
              : 'Not Started yet'}
          </h1>
        </div>
        <Button>Download File</Button>
      </div>

      {/* Table */}
      <LoanRequirementDetailsTable
        data={details?.farmerLoanRequirementItems || []}
        columns={columnLoanRequirementDetails}
      />
    </div>
  );
}
