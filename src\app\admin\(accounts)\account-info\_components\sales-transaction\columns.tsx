'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';

import { getUserType } from '@/lib/constants';

export const columns = [
  // {
  //   id: 'date',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Date" />,
  //   cell: ({ row }) => {
  //     const data = row.original;
  //     return (
  //       <div className="min-w-max">{`${new Date(data.entry_time ?? data.created_at).toLocaleDateString('en-US', {
  //         day: '2-digit',
  //         month: 'short',
  //         year: 'numeric',
  //       })}`}</div>
  //     );
  //   },
  //   accessorFn: (row) => {
  //     const data = row;
  //     return `${new Date(data.entry_time ?? data.created_at).toLocaleDateString('en-US', {
  //       day: '2-digit',
  //       month: 'short',
  //       year: 'numeric',
  //     })}`;
  //   },
  // },
  {
    id: 'transaction_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.created_at), 'MMM dd, yyyy')}</div>;
    },
    accessorFn: (row) => {
      const data = row;
      return format(new Date(data.created_at), 'MMM dd, yyyy');
    },
  },
  {
    id: 'noOfCrops',
    header: ({ column }) => <DataTableColumnHeader column={column} title="No. of Crops" />,
    cell: ({ row }) => {
      const data = row.original;
      return data.crops.length;
    },
    accessorFn: (row) => `${row.crops.length}`,
  },
  {
    id: 'totalCropsWeight',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Total Crops Weight" />,
    cell: ({ row }) => {
      const data = row.original;
      return `${(data.entry_weight - data.exit_weight).toLocaleString()} kg`;
    },
    accessorFn: (row) => `${row.entry_weight - row.exit_weight}`,
  },
  {
    id: 'transaction_value',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Transaction Value" />,
    cell: ({ row }) => {
      const data = row.original;
      return `${Number(data.gross_sales ?? 0).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      })}`;
    },
    accessorFn: (row) => `${row.gross_sales}`,
  },
  // {
  //   id: 'grossProfit',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Gross Profit" />,
  //   cell: ({ row }) => {
  //     const data = row.original;
  //     return `${Number(data.gross_profit ?? 0).toLocaleString('en-US', {
  //       style: 'currency',
  //       currency: 'PHP',
  //     })}`;
  //   },
  //   accessorFn: (row) => `${row.gross_profit}`,
  // },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    cell: ({ row }) => {
      const data = row.original;
      const user = getUserType(data.processedBy.user_type);
      return (
        <div className="min-w-max">
          {data.processedBy[user]
            ? `${data.processedBy[user]['first_name']} ${data.processedBy[user]['last_name']}`
            : data.processedBy.email}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      const user = getUserType(data.processedBy.user_type);
      return data.processedBy[user]
        ? `${data.processedBy[user]['first_name']} ${data.processedBy[user]['last_name']}`
        : data.processedBy.email;
    },
  },
  {
    id: 'date_encoded',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Encoded" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.updated_at), 'MMM dd, yyyy | hh:mm a')}</div>;
    },
    accessorFn: (row) => {
      const data = row;
      return format(new Date(data.updated_at), 'MMM dd, yyyy | hh:mm a');
    },
  },
];
