Sprint 16 (April 21-25)
🐛 fix(profile): address form "Same as Present" functionality
🐛 fix(farm-details): Move the "Pricing Determine By" to KYC at Farm Details tab

Sprint 14 (April 7-11)
✨ feat(farm-details): add new "Price Determined By" field with BAPTC and NVAT options
🔧 fix(identification-docs): make ID and utility fields conditionally required
✨ feat(roles): add Agronomist role to user management options
✨ feat(auth): add agronomist role with login capability
✨ feat(agronomist): implement agronomist layout and settings

- Add Agronomist role (ID: 50) to ROLES constant
- Update getUserType function to detect agronomist role
- Add agronomist to roles array and path mappings for authentication
- Initialize agronomist state in global store
- Improve username validation to accept both email and username formats

Sprint 13 (April 2-4)
🔨 refactor: reorganize account profile components into subdirectories
✨ feat(basic-info): implement permanent address functionality
✨ feat(career-academic): add occupation title field to career-academic form
✨ feat(account-profile): add source of funds
💄 feat(family-and-references): adapt form fields based on occupation type
✨ feat(property-ownership): add land bank accounts field

Sprint 11 (March 17-21)
✅ Price Monitoring: add bulk import
✅ refactor(price-history): update data fetching implementation
✅ feat(trading-app): Convert user search to multi-select in Add Group User dialog
Prod Deployment:

- crops with paginated and filterable status
- Price Monitoring: add bulk import
- refactor(price-history): update data fetching implementation
- Price Monitoring: add bulk import

Sprint 10 (March 10-14)
✅ Refactor crops with paginated and filterable status

Sprint 9 (March 3-7)
✅ Topup: Allow 0 in crop insurance amount
✅ Add Dashboard
✅ Allow username to login
✅ Allowed 0 on crop price
✅ deployed trading app, crop price range feature on production
✅ Farmer Account: Add Utility Bill Details
✅ Farmer Account: Add Mobile Device Details

Sprint 8 (Feb 24-28)
✅ Add List of Buyers Section in the Trading App → Buyer Tab
Note: User Rate & No. of Complaints are currently not working
✅ Soa Enhancement
✅ Trading App: Fix TC#109
✅ Trading App: Fix TC#110
✅ Create QA Account: Fix TC#44
✅ Edit Buyer Group: Fix TC#59
✅ Edit Seller Group: Fix TC#62
✅ Allowed 0 as percentage and logistics rate
✅ fix Price Monitoring bug
✅ Update add new price for single crop
✅ Update add new price validations

Sprint 7 (Feb 17 - 21)
✅ Add Group - Seller / Buyer: fix decimals
✅ Account Creation - QA: only superadmin can add accounts
✅ Account Creation - QA: don't allow speacial characters in name
✅ Identification Docs: don't require password
✅ Add multi export soa
