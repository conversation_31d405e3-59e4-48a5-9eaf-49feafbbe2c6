'use client';

// UI Components
import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';

// Hooks and Utilities
import useFarmer from '@/lib/hooks/useFarmer';
import useSelectAddress from '@/lib/hooks/utils/useSelectAddress';
import { useGlobalState } from '@/lib/store';

// Constants
import { PROFILE_TAB } from '../../../constants';
import AddressInfoForm from './components/address-info-form';
import BiometricInfoForm from './components/biometric-info-form';
import ContactInfoForm from './components/contact-info-form';
import PersonalInfoForm from './components/personal-info-form';
import PhysicalAttributesForm from './components/physical-attributes-form';
import ResidenceInfoForm from './components/residence-info-form';

export default function BasicProfile() {
  /**
   * STATE AND HOOKS
   */
  // Global state and hooks
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);
  const address = useSelectAddress();
  const addressPermanent = useSelectAddress();
  const isSamePresent = useHookstate(false);

  // Form initialization
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    setValue,
    getValues,
  } = useForm({
    defaultValues: {
      // Personal information
      firstName: data.farmer.first_name.value || '',
      lastName: data.farmer.last_name.value || '',
      middleName: data.farmer.middle_name?.value || '',
      birthDate: data.farmer.birth_date?.value || '',
      placeOfBirth: data.farmer.place_of_birth?.value || '',
      gender: data.farmer.gender?.value || '',
      religion: data.farmer.religion?.value || '',
      height: data.farmer.height?.value || '',
      weight: data.farmer.weight?.value || '',
      nationality: data.farmer.farmerInfo?.nationality?.value || '',

      // Contact information
      mobileNumber: data.farmer.mobile_number?.value || '',
      telephoneNumber: data.farmer.telephone_number?.value || '',
      otherMobileNumber: data.farmer.farmerInfo?.other_mobile_number?.value || '',
      facebookName: data.farmer.facebook_name?.value || '',

      // Present Address
      addressRegion: '',
      addressProvince: '',
      addressCity: '',
      addressBarangay: '',
      addressStreet: '',
      addressHouseNumber: '',
      addressZipCode: '',
      addressLengthOfStay: data.farmer?.address_length_of_stay?.value || '',
      residenceOwnership: data.farmer.farmerInfo?.residence_ownership?.value || '',

      // Permanent Address
      permanentAddressRegion: '',
      permanentAddressProvince: '',
      permanentAddressCity: '',
      permanentAddressBarangay: '',
      permanentAddressStreet: '',
      permanentAddressHouseNumber: '',
      permanentAddressZipCode: '',
      permanentAddressLengthOfStay: data.farmer?.permanent_address_length_of_stay?.value || '',

      // Other information
      hasBiometric: data.farmer.has_biometric.value === 1 ? true : false,
      facialRecognition: null,
    },
  } as any);

  /**
   * FORM SUBMISSION
   */
  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      weight: _data.weight ? _data.weight : 0,
      height: _data.height ? _data.height : 0,
      hasBiometric: _data.hasBiometric ? 1 : 0,
      userId: data.farmer.user_id.value,
    };

    // Handle facial recognition file upload
    if (_data.facialRecognition && _data.facialRecognition[0]) {
      updatedData = {
        ...updatedData,
        facialRecognition: _data.facialRecognition[0],
      };
    }

    console.log('Basic Info: ', updatedData);

    updateFarmer(updatedData);
    dirty.set(false);
  };

  // Update dirty state when form changes
  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  /**
   * ADDRESS INITIALIZATION
   * These useEffect hooks initialize address fields from saved data
   */
  // Update Region
  useEffect(() => {
    // Check if present and permanent address are the same
    const isSame = (() => {
      const presentAddress = JSON.parse(data.farmer.address.value);
      const permanentAddress = JSON.parse(data.farmer.permanent_address.value);

      if (!permanentAddress || !presentAddress) {
        return false;
      }

      return (
        presentAddress.addressRegion === permanentAddress.permanentAddressRegion &&
        presentAddress.addressProvince === permanentAddress.permanentAddressProvince &&
        presentAddress.addressCity === permanentAddress.permanentAddressCity &&
        presentAddress.addressBarangay === permanentAddress.permanentAddressBarangay &&
        presentAddress.addressHouseNumber === permanentAddress.permanentAddressHouseNumber &&
        presentAddress.addressStreet === permanentAddress.permanentAddressStreet &&
        presentAddress.addressZipCode === permanentAddress.permanentAddressZipCode
      );
    })();
    if (isSame) {
      isSamePresent.set(true);
    }

    if (address.regionList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address.value);
      const _province = JSON.parse(addressDetails?.addressProvince ?? '{}');

      if (_province?.region_code) {
        const _region = address.getRegionByCode(_province.region_code);
        address.setRegionSelected(_region);
        setValue('addressRegion', JSON.stringify(_region));
      }
    }
  }, [address.regionList]);

  // Update Province
  useEffect(() => {
    if (address.provinceList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address.value);
      const _province = JSON.parse(addressDetails?.addressProvince ?? '{}');

      if (_province?.province_code) {
        const __province = address.getProvinceByCode(_province.province_code);
        address.setProvinceSelected(__province);
        address.setStreet(addressDetails.addressStreet);
        address.setHouseNo(addressDetails.addressHouseNumber);
        address.setPostalCode(addressDetails.addressZipCode);

        setValue('addressProvince', JSON.stringify(__province));
        setValue('addressStreet', addressDetails.addressStreet);
        setValue('addressHouseNumber', addressDetails.addressHouseNumber);
        setValue('addressZipCode', String(addressDetails.addressZipCode || ''));
      }
    }
  }, [address.provinceList]);

  // Update City
  useEffect(() => {
    if (address.cityList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address.value);
      const _city = JSON.parse(addressDetails?.addressCity ?? '{}');

      if (_city?.region_desc) {
        delete Object.assign(_city, { ['region_code']: _city['region_desc'] })['region_desc'];
      }

      if (_city?.city_code) {
        const __city = address.getCityByCode(_city.city_code);
        address.setCitySelected(__city);
        setValue('addressCity', JSON.stringify(_city));
      }
    }
  }, [address.cityList]);

  // Update Barangay
  useEffect(() => {
    if (address.barangayList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.address.value);
      const _brgy = JSON.parse(addressDetails?.addressBarangay ?? '{}');

      if (_brgy?.brgy_code) {
        const __barangay = address.getBarangayByCode(_brgy.brgy_code);
        address.setBarangaySelected(__barangay);
        setValue('addressBarangay', JSON.stringify(__barangay));
      }
    }
  }, [address.barangayList]);

  /**
   * PERMANENT ADDRESS INITIALIZATION
   * These useEffect hooks initialize address fields from saved data
   */
  // Update Region
  useEffect(() => {
    if (addressPermanent.regionList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.permanent_address.value);
      let _province = JSON.parse(addressDetails?.permanentAddressProvince ?? '{}');

      const addressDetailsPresent = JSON.parse(data.farmer.address.value);
      const _provincePresent = JSON.parse(addressDetailsPresent?.addressProvince ?? '{}');

      if (isSamePresent.value) {
        _province = _provincePresent;
      }

      if (_province?.region_code) {
        const _region = addressPermanent.getRegionByCode(_province.region_code);
        addressPermanent.setRegionSelected(_region);
        setValue('permanentAddressRegion', JSON.stringify(_region));
      }
    }
  }, [addressPermanent.regionList]);

  // Update Province
  useEffect(() => {
    if (addressPermanent.provinceList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.permanent_address.value);
      let _province = JSON.parse(addressDetails?.permanentAddressProvince ?? '{}');

      const addressDetailsPresent = JSON.parse(data.farmer.address.value);
      const _provincePresent = JSON.parse(addressDetailsPresent?.addressProvince ?? '{}');

      if (isSamePresent.value) {
        _province = _provincePresent;
      }

      if (_province?.province_code) {
        const __province = addressPermanent.getProvinceByCode(_province.province_code);
        addressPermanent.setProvinceSelected(__province);

        setValue('permanentAddressProvince', JSON.stringify(__province));

        // Only set form values when NOT same as present address
        // When same as present, let the checkbox handler manage the form values
        if (!isSamePresent.value) {
          addressPermanent.setStreet(addressDetails.permanentAddressStreet);
          addressPermanent.setHouseNo(addressDetails.permanentAddressHouseNumber);
          addressPermanent.setPostalCode(addressDetails.permanentAddressZipCode);

          setValue('permanentAddressHouseNumber', addressDetails.permanentAddressHouseNumber);
          setValue('permanentAddressStreet', addressDetails.permanentAddressStreet);
          setValue('permanentAddressZipCode', String(addressDetails.permanentAddressZipCode || ''));
          setValue('permanentAddressLengthOfStay', data.farmer?.permanent_address_length_of_stay?.value);
        }
      }
    }
  }, [addressPermanent.provinceList]);

  // Update City
  useEffect(() => {
    if (addressPermanent.cityList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.permanent_address.value);
      let _city = JSON.parse(addressDetails?.permanentAddressCity ?? '{}');

      const addressDetailsPresent = JSON.parse(data.farmer.address.value);
      const _cityPresent = JSON.parse(addressDetailsPresent?.addressCity ?? '{}');

      if (isSamePresent.value) {
        _city = _cityPresent;
      }

      if (_city?.region_desc) {
        delete Object.assign(_city, { ['region_code']: _city['region_desc'] })['region_desc'];
      }

      if (_city?.city_code) {
        const __city = addressPermanent.getCityByCode(_city.city_code);
        addressPermanent.setCitySelected(__city);
        setValue('permanentAddressCity', JSON.stringify(_city));
      }
    }
  }, [addressPermanent.cityList]);

  // Update Barangay
  useEffect(() => {
    if (addressPermanent.barangayList.length > 0) {
      const addressDetails = JSON.parse(data.farmer.permanent_address.value);
      let _brgy = JSON.parse(addressDetails?.permanentAddressBarangay ?? '{}');

      const addressDetailsPresent = JSON.parse(data.farmer.address.value);
      const _brgyPresent = JSON.parse(addressDetailsPresent?.addressBarangay ?? '{}');

      if (isSamePresent.value) {
        _brgy = _brgyPresent;
      }

      if (_brgy?.brgy_code) {
        const __barangay = addressPermanent.getBarangayByCode(_brgy.brgy_code);
        addressPermanent.setBarangaySelected(__barangay);
        setValue('permanentAddressBarangay', JSON.stringify(__barangay));
      }
    }
  }, [addressPermanent.barangayList]);

  return (
    <form id={PROFILE_TAB[0].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="">
        {/* PERSONAL INFORMATION SECTION */}
        <PersonalInfoForm register={register} control={control} errors={errors} />

        {/* PHYSICAL ATTRIBUTES SECTION */}
        <PhysicalAttributesForm register={register} control={control} errors={errors} />

        {/* CONTACT INFORMATION SECTION */}
        <ContactInfoForm register={register} control={control} errors={errors} />

        {/* ADDRESS INFORMATION SECTION */}
        <AddressInfoForm
          register={register}
          control={control}
          errors={errors}
          address={address}
          addressPermanent={addressPermanent}
          getValues={getValues}
          setValue={setValue}
          isSamePresent={isSamePresent}
        />

        {/* RESIDENCE INFORMATION SECTION */}
        <ResidenceInfoForm register={register} control={control} errors={errors} />

        {/* OTHER INFORMATION SECTION */}
        <BiometricInfoForm register={register} control={control} errors={errors} />
      </div>
    </form>
  );
}
