'use client';

import { useHookstate } from '@hookstate/core';
import { PlusIcon, XIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Input, InputSign } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import useTradingPost from '@/lib/hooks/admin/useTradingPost';
import useCrops from '@/lib/hooks/useCrops';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn, urlify } from '@/lib/utils';

export default function AddManualTransactionPage() {
  const router = useRouter();
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const { getCropsPublic } = useCrops();
  const { addTransaction } = useTradingPost();

  const data = gStateP.selected['farmer'].get({ noproxy: true });
  const address = data && data['farmer']['address'] ? JSON.parse(data['farmer']['address']) : {};
  const publicCrops = gState.admin.crops.data.get({ noproxy: true });
  const loading = useHookstate(false);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    getValues,
    reset,
    watch,
  } = useForm({
    defaultValues: {
      transactionDate: '',
      entries: [
        {
          cropId: '',
          itemWeight: '',
          sellingPrice: '',
          productionPrice: '0',
        },
      ],
    },
  });
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'entries',
  });
  const watchFields = watch('entries');

  const onSubmit = async (data) => {
    try {
      loading.set(true);
      const updatedData = {
        ...data,
        farmerId: gStateP.selected['farmer']['farmer']['id'].value,
      };
      console.log('onSubmit: ', updatedData);

      await addTransaction(updatedData);
      router.back();
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  useEffect(() => {
    getCropsPublic();
  }, []);

  return (
    <div className="flex-1 p-8">
      <h1 className="text-3xl font-bold tracking-tight">Add Transaction</h1>
      <Breadcrumb className="mt-2">
        <BreadcrumbList>
          <BreadcrumbItem className="cursor-pointer">
            <BreadcrumbLink onClick={() => router.back()}>Account Information</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Add Transaction</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {data && (
        <div className="mt-6 grid gap-8">
          {/* Account Information */}
          <div className="card rounded-[20px]">
            <div className="flex gap-8">
              <div className="">
                {/* Profile Image */}
                <div>
                  <img
                    className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                    src={data.user_img ? urlify(data.user_img, 'users/profile') : '/assets/user-default.jpg'}
                    alt=""
                  />
                </div>
              </div>

              <div className="flex-1">
                <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
                <dl className="grid grid-cols-2 gap-4">
                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data.farmer.first_name} ${data.farmer.last_name}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`ID${data.id.toString().padStart(9, '0')}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${data.farmer.mobile_number ?? ''}`}
                      </dd>
                    </div>
                  </div>

                  <div className="font-dmSans">
                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                        {`${new Date(data.farmer.birth_date).toLocaleString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}`}
                      </dd>
                    </div>

                    <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                      <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.email}`}</dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>
          </div>

          <form id="add-transaction" onSubmit={handleSubmit(onSubmit)} className="grid gap-8">
            <div className="flex items-center justify-between gap-4">
              <div>
                <div className="font-dmSans text-xl font-bold text-primary">Add Transaction</div>
                <div className="mt-0.5 text-sm text-muted-foreground">Input the details of the Transaction.</div>
              </div>

              <div>
                <div className="flex items-center gap-2">
                  <label htmlFor="transactionDate" className="text-sm font-bold text-kitaph-primary">
                    <span className="mr-1.5 text-red-500">*</span>
                    <span>Transaction Date</span>
                  </label>

                  <div className="grid items-center gap-1.5">
                    <Input
                      {...register(`transactionDate`, {
                        required: 'Transaction date is required',
                      })}
                      type="date"
                      className={cn(errors.transactionDate && 'border-red-500')}
                    />
                  </div>
                </div>

                {errors.transactionDate && (
                  <p className="form-error text-right">{`${errors.transactionDate?.message}`}</p>
                )}
              </div>
            </div>

            <div className="card overflow-hidden border p-0">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50 hover:bg-gray-50">
                    <TableHead>Crop</TableHead>
                    <TableHead>Item Weight</TableHead>
                    <TableHead>GS Price per kilo</TableHead>
                    <TableHead>Gross Sales</TableHead>
                    {/* <TableHead>GP Price per kilo</TableHead>
                    <TableHead>Gross Profit</TableHead> */}
                    <TableHead>
                      <span className="sr-only">Action</span>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fields.map((field, index) => {
                    const errorField = errors?.entries?.[index];
                    const isLastIndex = index === fields.length - 1;

                    return (
                      <TableRow key={field.id}>
                        <TableCell className="min-w-48">
                          <div className="grid items-center gap-1.5">
                            <Controller
                              control={control}
                              name={`entries.${index}.cropId` as const}
                              rules={{ required: 'Crop is required' }}
                              render={({ field: { onChange, onBlur, value, ref } }) => (
                                <Select onValueChange={onChange} value={value}>
                                  <SelectTrigger
                                    className={cn(
                                      'focus-visible:ring-primary',
                                      errorField?.cropId &&
                                        'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                                    )}
                                  >
                                    <SelectValue placeholder="Select crop" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectGroup>
                                      {publicCrops.map((v) => {
                                        return (
                                          <SelectItem key={v.id} value={`${v.id}`}>
                                            {v.name}
                                          </SelectItem>
                                        );
                                      })}
                                    </SelectGroup>
                                  </SelectContent>
                                </Select>
                              )}
                            />
                            {errorField?.cropId && <p className="form-error">{`${errorField?.cropId?.message}`}</p>}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="grid items-center gap-1.5">
                            <InputSign
                              {...register(`entries.${index}.itemWeight`, {
                                required: 'Item weight is required',
                                validate: (v) => (v && Number(v) > 0) || 'Item weight must be greater than 0',
                              })}
                              type="number"
                              min={0}
                              sign="kg"
                              placeholder="Enter item weight"
                            />
                            {errorField?.itemWeight && (
                              <p className="form-error">{`${errorField?.itemWeight?.message}`}</p>
                            )}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="grid items-center gap-1.5">
                            <InputSign
                              {...register(`entries.${index}.sellingPrice`, {
                                required: 'Selling price is required',
                                validate: (v) => (v && Number(v) > 0) || 'Selling price must be greater than 0',
                              })}
                              type="number"
                              min={0}
                              sign="Php"
                              step="any"
                              placeholder="0.00"
                            />
                            {errorField?.sellingPrice && (
                              <p className="form-error">{`${errorField?.sellingPrice?.message}`}</p>
                            )}
                          </div>
                        </TableCell>

                        <TableCell>
                          {Number(
                            Number(watchFields[index].itemWeight) * Number(watchFields[index].sellingPrice),
                          ).toLocaleString('en-US', {
                            style: 'currency',
                            currency: 'PHP',
                          })}
                        </TableCell>
                        {/* 
                        <TableCell>
                          <div className="grid items-center gap-1.5">
                            <InputSign
                              {...register(`entries.${index}.productionPrice`, {
                                required: 'Production price is required',
                                validate: (v) => (v && Number(v) > 0) || 'Production price must be greater than 0',
                              })}
                              type="number"
                              min={0}
                              sign="Php"
                              placeholder="0.00"
                            />
                            {errorField?.productionPrice && (
                              <p className="form-error">{`${errorField?.productionPrice?.message}`}</p>
                            )}
                          </div>
                        </TableCell>

                        <TableCell>
                          {Number(
                            Number(watchFields[index].itemWeight) * Number(watchFields[index].productionPrice),
                          ).toLocaleString('en-US', {
                            style: 'currency',
                            currency: 'PHP',
                          })}
                        </TableCell>
                         */}
                        <TableCell className="flex justify-center gap-3">
                          {index > 0 && (
                            <Button
                              className=""
                              variant="destructive"
                              size="icon"
                              onClick={() => {
                                remove(index);
                              }}
                            >
                              <XIcon className="size-4" />
                            </Button>
                          )}
                          {isLastIndex && (
                            <Button
                              className=""
                              variant="outline"
                              size="icon"
                              onClick={() => {
                                append({ cropId: '', itemWeight: '', sellingPrice: '', productionPrice: '0' });
                              }}
                            >
                              <PlusIcon className="size-4" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
                <TableFooter>
                  <TableRow>
                    <TableCell colSpan={3}>Total</TableCell>
                    <TableCell className="text-left">
                      {watchFields
                        .reduce((a, b) => Number(a) + Number(b.itemWeight) * Number(b.sellingPrice), 0)
                        .toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                    </TableCell>
                    {/* <TableCell colSpan={2} className="text-right">
                      {watchFields
                        .reduce((a, b) => Number(a) + Number(b.itemWeight) * Number(b.productionPrice), 0)
                        .toLocaleString('en-US', {
                          style: 'currency',
                          currency: 'PHP',
                        })}
                    </TableCell> */}
                    <TableCell></TableCell>
                  </TableRow>
                </TableFooter>
              </Table>
            </div>
          </form>

          <div className="flex justify-end">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button size="lg" type="button">
                  Submit
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent className="font-sans">
                <AlertDialogHeader>
                  <AlertDialogTitle>Confirmation</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you confirming that you intend to add this transaction?
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Review Items</AlertDialogCancel>
                  {loading.value ? (
                    <ButtonLoading />
                  ) : (
                    <AlertDialogAction form="add-transaction" type="submit">
                      Confirm
                    </AlertDialogAction>
                  )}
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
      )}
    </div>
  );
}
