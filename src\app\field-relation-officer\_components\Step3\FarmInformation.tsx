import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { farmInformationSchema, TFarmInformationSchema } from '../../schemas';
import { IFarmInformation, IFROState } from '../../types';
import FarmLocation from './FarmLocation';
import FarmPractices from './FarmPractices';

interface IFarmInformationProps {
  gStateFRO: State<IFROState, LocalStored>;
  onPrevious: () => void;
  onNext: () => void;
  onClear: () => void;
}

const FarmInformation = ({ gStateFRO, onPrevious, onNext, onClear }: IFarmInformationProps) => {
  const data = gStateFRO?.form.step3?.value;
  const {
    register,
    control,
    formState: { errors },
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TFarmInformationSchema>({
    resolver: zodResolver(farmInformationSchema),
    defaultValues: {
      // Farm Location
      farmAddressHouseNumber: data?.farmAddressHouseNumber || '',
      farmAddressStreet: data?.farmAddressStreet || '',
      farmAddressRegion: data?.farmAddressRegion || '',
      farmAddressProvince: data?.farmAddressProvince || '',
      farmAddressCity: data?.farmAddressCity || '',
      farmAddressBarangay: data?.farmAddressBarangay || '',
      farmAddressZipCode: data?.farmAddressZipCode || '',
      farmArea: data?.farmArea || '',
      farmOwnership: data?.farmOwnership || '',
      otherFarmOwnership: data?.otherFarmOwnership || '',
      cropsPlanted: data?.cropsPlanted?.slice() || [],

      // Farm Practices
      averageYieldPerYear: data?.averageYieldPerYear || '',
      waterSource: data?.waterSource || '',
      waterSourceOthers: data?.waterSourceOthers || '',
      fertilizerUsed: data?.fertilizerUsed || '',
      pesticideUsed: data?.pesticideUsed || '',
      farmImplements: data?.farmImplements || '',
      farmImplementsOthers: data?.farmImplementsOthers || '',
    },
  });

  const handleNext = handleSubmit(() => onNext());

  const handlePrevious = () => onPrevious();

  useEffect(() => {
    const subscription = watch((value) => {
      gStateFRO.form.step3.set(value as IFarmInformation);
    });

    return () => subscription.unsubscribe();
  }, [watch]);

  console.log('Step3:', { errors, values: values() });

  return (
    <form onSubmit={handleNext}>
      <FarmLocation register={register} control={control} errors={errors} values={values} watch={watch} />
      <FarmPractices register={register} control={control} errors={errors} />

      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </div>
    </form>
  );
};

export default FarmInformation;
