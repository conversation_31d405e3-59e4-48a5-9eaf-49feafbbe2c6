'use client';

import React from 'react';

import { FormDimensions } from '@/lib/constants/enums';

const Page3 = ({ data }) => {
  const address = data && data['address'] ? JSON.parse(data['address']) : {};

  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.toLocaleDateString('en-US', { month: 'long' });
  const currentDay = currentDate.getDate();

  return (
    <div className={`relative flex flex-col border bg-white p-20 print:border-none ${FormDimensions.LEGAL}`}>
      {/* Section 8 - Pag-Default at mga Remedyo nito */}
      <div className="mb-6 text-sm">
        <div className="mb-4 font-bold">8. Pag-Default at mga Remedyo nito</div>
        <div className="mb-4">
          <span className="font-medium">Kung sakali mang hindi sumunod ang isang Partido:</span>
        </div>

        <div className="mb-4 space-y-3 text-sm leading-relaxed">
          <div className="flex">
            <span className="mr-2">a.</span>
            <span className="text-justify">
              Partner-Farmer Default: Ang hindi pagsunod sa loan repayment schedule o ang maling paggamit ng mga
              Agri-Input ay nangangahulugan ng pagsira sa Kasunduang ito. Ang KITA ay may karapatang pansamantalang
              itigil ang technical assistance nito at maaaring gumawa ng mga legal na hakbang na pinahihintulutan ng
              batas.
            </span>
          </div>

          <div className="flex">
            <span className="mr-2">b.</span>
            <span className="text-justify">
              KITA Default: Maaaring ipatigiI ng Partner-Farmer ang Kasunduang ito kung ng hindi sumunod ang KITA sa
              pag-deliver ng mga Agri-Inputs o hindi pagbibigay ng technical support na ayon sa Kasunduang ito. Maaaring
              ito ay ipatigiI sa pagbibigay ng paunang written notice at maaari ding humihingi ng danyos ang
              Partner-Farmer sa mga losses nito.
            </span>
          </div>
        </div>
      </div>

      {/* Section 9 - Pagtatapos ng Kasunduan */}
      <div className="mb-6 text-sm">
        <div className="mb-4 font-bold">9. Pagtatapos ng Kasunduan</div>
        <div className="text-justify leading-relaxed">
          Ang kasunduang ito ay maaaring tapusin ng alinmang partido sa pamamagitan ng nakasulat na abiso tatlumpung
          (30) araw bago ang aktwal na pagtatapos.
        </div>
      </div>

      {/* Section 10 - Iba pang mga Probisyon */}
      <div className="pb-8 text-sm">
        <div className="mb-4 font-bold">10. Iba pang mga Probisyon</div>
        <div className="space-y-3 leading-relaxed">
          <div className="flex">
            <span className="mr-2">a.</span>
            <span className="text-justify">
              Ang anumang bahagi ng Kasunduang ito na mapapawalan-bisa dahil ito ay salungat sa Batas ng Republika ng
              Pilipinas ay hindi makakaapekto sa bisa ng ibang bahagi ng Kasunduan at mananatiling may-bisa;
            </span>
          </div>

          <div className="flex">
            <span className="mr-2">b.</span>
            <span className="text-justify">
              Ang Kasunduang ito ay magiging epektibo sa parehong partido, pari na rin sa kanilang mga kahalili at mga
              tagapagmana;
            </span>
          </div>

          <div className="flex">
            <span className="mr-2">c.</span>
            <span className="text-justify">
              Ang Kasunduang ito ay magiging epektibo sa sandaling ito ay mapirmahan ng parehong partido.
            </span>
          </div>
        </div>
      </div>

      {/* Date and Signature Section */}
      <div className="mb-8 text-sm">
        <div className="mb-6">
          <span>Nilagdaan ngayong ika-</span>
          <span className="mx-2 border-b border-black px-4 text-center"></span>
          <span>araw ng</span>
          <span className="mx-2 border-b border-black px-8 text-center"></span>
          <span>, {currentYear} sa</span>
          <span className="mx-2 border-b border-black px-12 text-center"></span>
          <span>.</span>
        </div>
      </div>

      {/* Signature Blocks */}
      <div className="flex justify-between">
        {/* KITA AGRITECH CORP */}
        <div className="w-1/2 pr-8">
          <div className="mb-2 text-center font-bold">KITA AGRITECH CORP</div>
          <div className="mb-1 text-sm">Ni:</div>
          <div className="mb-8 h-16 border-b border-black"></div>
          <div className="text-center text-sm">Earwin A. Belen, MSc., R.Agr.</div>
        </div>

        {/* Partner-Farmer */}
        <div className="w-1/2 pl-8">
          <div className="mb-2 text-center font-bold">Partner-Farmer</div>
          <div className="mb-1 text-sm">Ni:</div>
          <div className="mb-8 h-16 border-b border-black"></div>
          <div className="text-center text-sm capitalize">
            {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim() ||
              '(Name of Farmer)'}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page3;
