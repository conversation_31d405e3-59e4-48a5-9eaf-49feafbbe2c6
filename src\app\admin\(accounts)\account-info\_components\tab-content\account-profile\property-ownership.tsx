'use client';

import { useHookstate } from '@hookstate/core';
import { Plus, X } from 'lucide-react';
import { useEffect } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import useFarmer from '@/lib/hooks/useFarmer';
import usePublic from '@/lib/hooks/usePublic';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

import { BANK_NAMES, PROFILE_TAB } from '../../constants';
import { IFarmPropertyEnum } from './Enums';

const OPTION_VEHICLE_OWNED = [
  {
    label: 'CAR',
    value: 'CAR',
  },
  {
    label: 'TRUCK',
    value: 'TRUCK',
  },
  {
    label: 'TRICYCLE',
    value: 'TRICYCLE',
  },
  {
    label: 'MOTORCYCLE',
    value: 'MOTORCYCLE',
  },
];

const OPTION_LAND_BANK_ACCOUNTS = [
  {
    label: 'SA-ATM',
    value: 'SA-ATM',
  },
  {
    label: 'CA-ATM',
    value: 'CA-ATM',
  },
  {
    label: 'TIME DEPOSIT',
    value: 'TIME DEPOSIT',
  },
  {
    label: 'TRUST',
    value: 'TRUST',
  },
  {
    label: 'LOANS',
    value: 'LOANS',
  },
  {
    label: 'TREASURY',
    value: 'TREASURY',
  },
  {
    label: 'TRADE',
    value: 'TRADE',
  },
  {
    label: 'CREDIT CARD',
    value: 'CREDIT CARD',
  },
];

export default function PropertyOwnership() {
  const gState = useGlobalState();
  const { updateFarmer } = useFarmer();
  const { getAllCrops, OPTION_CROPS } = usePublic();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const dirty = useHookstate(gState.selected.accountInfo.tabs.isDirty);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    watch,
  } = useForm({
    defaultValues: {
      vehicleOwned: data?.farmer?.vehicle_owned?.value
        ? data?.farmer?.vehicle_owned?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      landbankAccounts: data?.farmer?.landbank_accounts?.value
        ? data?.farmer?.landbank_accounts?.value.split(',').map((s) => ({ label: s, value: s }))
        : [],
      realProperty: data?.farmer?.realProperties?.value.map((s) => ({
        propertyOwned: s.property_owned,
        propertyTitleNumber: s.property_title_number,
        propertyLotNumber: s.property_lot_number,
        propertyLocation: s.property_location,
        propertyLotArea: s.property_lot_area,
        cropsProduce: s.crops_produce?.split(',').map((c) => ({ label: c, value: c })) || [],
      })),
      bankDetail:
        data?.farmer?.farmerBankDetails.length > 0
          ? data?.farmer?.farmerBankDetails?.value.map((s) => ({
              bankName: s.bank_name,
              bankAccountType: s.bank_account_type,
              bankAccountNumber: s.bank_account_number,
            }))
          : [
              {
                bankName: '',
                bankAccountType: '',
                bankAccountNumber: '',
              },
            ],
    },
  });
  const realProperty = useFieldArray({
    name: 'realProperty',
    control,
  });
  const bankDetail = useFieldArray({
    name: 'bankDetail',
    control,
  });

  const onSubmit = (_data: any) => {
    let updatedData = {
      ..._data,
      realProperty: _data.realProperty.map((s) => ({
        ...s,
        cropsProduce: s.cropsProduce.map((c) => c.value).join(','),
      })),
      vehicleOwned: _data.vehicleOwned.map((s) => s.value).join(','),
      landbankAccounts: _data.landbankAccounts.map((s) => s.value).join(','),
      userId: data.farmer.user_id.value,
    };

    console.log('Property Ownership: ', updatedData);
    updateFarmer(updatedData);
    dirty.set(false);
  };

  useEffect(() => {
    dirty.set(isDirty);
  }, [isDirty]);

  useEffect(() => {
    getAllCrops();
  }, []);

  return (
    <form id={PROFILE_TAB[4].value} onSubmit={handleSubmit(onSubmit)}>
      <div className="space-y-4">
        <div className="space-y-4 divide-y-2 divide-dashed">
          {realProperty.fields.map((field, index) => {
            const errorForField = errors?.realProperty?.[index];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3 pr-16',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Property Owned */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`realProperty.${index}.propertyOwned`} className="pb-1 font-normal">
                    Property Owned
                  </Label>
                  <Controller
                    control={control}
                    name={`realProperty.${index}.propertyOwned` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.propertyOwned &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Property Owned" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {Object.values(IFarmPropertyEnum).map((property) => (
                              <SelectItem key={property} value={property}>
                                {property}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.propertyOwned && (
                    <p className="form-error">{`${errorForField?.propertyOwned?.message}`}</p>
                  )}
                </div>

                {/* Land Title No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`realProperty.${index}.propertyTitleNumber`} className="pb-1 font-normal">
                    Land Title No.
                  </Label>
                  <Input
                    {...register(`realProperty.${index}.propertyTitleNumber` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.propertyTitleNumber && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Title No."
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                  {errorForField?.propertyTitleNumber && (
                    <p className="form-error">{`${errorForField?.propertyTitleNumber?.message}`}</p>
                  )}
                </div>

                {/* Lot No. */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`realProperty.${index}.propertyLotNumber`} className="pb-1 font-normal">
                    Lot No.
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`realProperty.${index}.propertyLotNumber` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.propertyLotNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Lot No."
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => realProperty.remove(index)}
                        disabled={!gState.accountProfileIsEdit.value}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.propertyLotNumber && (
                    <p className="form-error">{`${errorForField?.propertyLotNumber?.message}`}</p>
                  )}
                </div>

                {/* Location */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`realProperty.${index}.propertyLocation`} className="pb-1 font-normal">
                    Location
                  </Label>
                  <Input
                    {...register(`realProperty.${index}.propertyLocation` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.propertyLocation && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Location"
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                  {errorForField?.propertyLocation && (
                    <p className="form-error">{`${errorForField?.propertyLocation?.message}`}</p>
                  )}
                </div>

                {/* Lot Area (sqm) */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`realProperty.${index}.propertyLotArea`} className="pb-1 font-normal">
                    Lot Area (sqm)
                  </Label>
                  <Input
                    {...register(`realProperty.${index}.propertyLotArea` as const)}
                    className={cn(
                      'focus-visible:ring-primary',
                      errorForField?.propertyLotArea && 'border-red-500 focus-visible:ring-red-500',
                    )}
                    type="text"
                    placeholder="Enter Lot Area (sqm)"
                    disabled={!gState.accountProfileIsEdit.value}
                  />
                  {errorForField?.propertyLotArea && (
                    <p className="form-error">{`${errorForField?.propertyLotArea?.message}`}</p>
                  )}
                </div>

                {/* blank */}
                <div></div>

                {[IFarmPropertyEnum.AGRICULTURAL].includes(
                  watch(`realProperty.${index}.propertyOwned`) as IFarmPropertyEnum,
                ) && (
                  <>
                    {/* Crops Produce */}
                    <div className="col-span-3 grid w-full items-center gap-1.5">
                      <Label htmlFor={`realProperty.${index}.cropsProduce`} className="pb-1 font-normal">
                        Crops Produce
                      </Label>
                      <Controller
                        control={control}
                        name={`realProperty.${index}.cropsProduce`}
                        render={({ field: { onChange, onBlur, value, ref } }) => (
                          <MultipleSelector
                            value={value}
                            onChange={onChange}
                            defaultOptions={OPTION_CROPS}
                            placeholder="Select from selection or create new"
                            creatable
                            emptyIndicator={
                              <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                                No results found.
                              </p>
                            }
                            disabled={!gState.accountProfileIsEdit.value}
                          />
                        )}
                      />
                      {errorForField?.cropsProduce && (
                        <p className="form-error">{`${errorForField?.cropsProduce?.message}`}</p>
                      )}
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              realProperty.append({
                propertyOwned: '',
                propertyTitleNumber: '',
                propertyLotNumber: '',
                propertyLocation: '',
                propertyLotArea: '',
                cropsProduce: [],
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add More Real Property</span>
          </Button>
        </div>

        {/* Vehicle Owned */}
        <div className="grid w-full max-w-sm items-center gap-1.5">
          <Label htmlFor="vehicleOwned" className="pb-1 font-normal">
            Vehicle Owned
          </Label>
          <Controller
            control={control}
            name="vehicleOwned"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_VEHICLE_OWNED}
                placeholder="Select from selection or create new"
                creatable
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
                disabled={!gState.accountProfileIsEdit.value}
              />
            )}
          />
          {errors.vehicleOwned && <p className="form-error">{`${errors.vehicleOwned.message}`}</p>}
        </div>

        <FormTitle title="Land Bank Accounts" />
        <FormField label="Existing Accounts with Land Bank of the Philippines" name="landbankAccounts" errors={errors}>
          <Controller
            control={control}
            name="landbankAccounts"
            render={({ field: { onChange, onBlur, value, ref } }) => (
              <MultipleSelector
                value={value}
                onChange={onChange}
                defaultOptions={OPTION_LAND_BANK_ACCOUNTS}
                placeholder="Select from selection"
                creatable={false}
                emptyIndicator={
                  <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">No results found.</p>
                }
                disabled={!gState.accountProfileIsEdit.value}
              />
            )}
          />
        </FormField>

        {/* Bank Details */}
        <div className="font-dmSans text-xl font-bold text-primary">Bank Details</div>

        <div className="space-y-4 divide-y-2 divide-dashed">
          {bankDetail.fields.map((field, index) => {
            const errorForField = errors?.bankDetail?.[index];

            return (
              <div
                key={field.id}
                className={cn(
                  'grid sm:grid-cols-2 xl:grid-cols-3 items-start gap-4 pb-3 pr-16',
                  index === 0 ? 'pt-0' : 'pt-7',
                )}
              >
                {/* Bank Name */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`bankDetail.${index}.bankName`} className="pb-1 font-normal">
                    Bank Name
                  </Label>
                  <Controller
                    control={control}
                    name={`bankDetail.${index}.bankName` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.bankName &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Bank Name" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            {BANK_NAMES.map((bank) => (
                              <SelectItem key={bank} value={bank}>
                                {bank}
                              </SelectItem>
                            ))}
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.bankName && <p className="form-error">{`${errorForField?.bankName?.message}`}</p>}
                </div>

                {/* Account Type */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`bankDetail.${index}.bankAccountType`} className="pb-1 font-normal">
                    Account Type
                  </Label>
                  <Controller
                    control={control}
                    name={`bankDetail.${index}.bankAccountType` as const}
                    render={({ field: { onChange, onBlur, value, ref } }) => (
                      <Select onValueChange={onChange} value={value} disabled={!gState.accountProfileIsEdit.value}>
                        <SelectTrigger
                          className={cn(
                            'focus-visible:ring-primary',
                            errorForField?.bankAccountType &&
                              'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                          )}
                        >
                          <SelectValue placeholder="Select Account Type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectGroup>
                            <SelectItem value="CHECKING ACCOUNT">CHECKING ACCOUNT</SelectItem>
                            <SelectItem value="SAVINGS ACCOUNT">SAVINGS ACCOUNT</SelectItem>
                          </SelectGroup>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errorForField?.bankAccountType && (
                    <p className="form-error">{`${errorForField?.bankAccountType?.message}`}</p>
                  )}
                </div>

                {/* Account Number */}
                <div className="grid w-full max-w-sm items-center gap-1.5">
                  <Label htmlFor={`bankDetail.${index}.bankAccountNumber`} className="pb-1 font-normal">
                    Account Number
                  </Label>
                  <div className="relative">
                    <Input
                      {...register(`bankDetail.${index}.bankAccountNumber` as const)}
                      className={cn(
                        'focus-visible:ring-primary',
                        errorForField?.bankAccountNumber && 'border-red-500 focus-visible:ring-red-500',
                      )}
                      type="text"
                      placeholder="Enter Account Number"
                      disabled={!gState.accountProfileIsEdit.value}
                    />
                    <div className={cn('absolute top-0 right-[-4rem]', index === 0 && 'invisible')}>
                      <Button
                        className="border-red-500 text-red-500 hover:bg-red-200 hover:text-red-600"
                        variant="outline"
                        size="icon"
                        onClick={() => bankDetail.remove(index)}
                        disabled={!gState.accountProfileIsEdit.value}
                      >
                        <X className="size-5" />
                      </Button>
                    </div>
                  </div>
                  {errorForField?.bankAccountNumber && (
                    <p className="form-error">{`${errorForField?.bankAccountNumber?.message}`}</p>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        <div className="flex pb-6 pt-2">
          <Button
            className="border-slate-300"
            type="button"
            onClick={() =>
              bankDetail.append({
                bankName: '',
                bankAccountType: '',
                bankAccountNumber: '',
              })
            }
            disabled={!gState.accountProfileIsEdit.value}
          >
            <Plus className="mr-2 size-5 text-white" />
            <span>Add Bank Details</span>
          </Button>
        </div>
      </div>
    </form>
  );
}
