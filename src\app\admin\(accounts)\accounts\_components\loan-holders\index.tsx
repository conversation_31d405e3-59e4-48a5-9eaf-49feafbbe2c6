'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { TabLoanHolderEnum } from '@/lib/constants/enums';
import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useMember from '@/lib/hooks/admin/useMember';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalStatePersist } from '@/lib/store/persist';

import { LoanHolderTable } from './loan-holder-table';
import { LoanHolderColumns } from './loan-holder-table/columns';
import { LoanApplicantsColumns } from './loan-holder-table/loan-applicants-columns';

export default function LoanHoldersPage() {
  const gStateP = useGlobalStatePersist();
  const {
    getLoanHolders,
    getLoanHoldersDashboard,
    paginationLoanHolder,
    loanHolders,
    getLoanApplicants,
    getLoanApplicantsDashboard,
    loanApplicants,
    paginationLoanApplicants,
  } = useMember();
  const { getGroups } = useCreditScoreMgt();

  const tabLoanHolder = useHookstate(gStateP.tabsLoanHolder);

  const pageSizeDebounce = useHookstateDebounce(paginationLoanHolder.pageSize, 500);
  const pageDebounce = useHookstateDebounce(paginationLoanHolder.page, 500);
  const creditScoreGroupIdsDebounce = useHookstateDebounce(paginationLoanHolder.creditScoreGroupIds, 500);
  const loanTermDebounce = useHookstateDebounce(paginationLoanHolder.loanTerm, 500);
  const paymentStatusesDebounce = useHookstateDebounce(paginationLoanHolder.paymentStatuses, 500);
  const startDateDebounce = useHookstateDebounce(paginationLoanHolder.startDate, 500);
  const endDateDebounce = useHookstateDebounce(paginationLoanHolder.endDate, 500);

  const loanBalanceStartDebounce = useHookstateDebounce(paginationLoanHolder.loanBalanceStart, 1000);
  const loanBalanceEndDebounce = useHookstateDebounce(paginationLoanHolder.loanBalanceEnd, 1000);
  const loanAmountStartDebounce = useHookstateDebounce(paginationLoanHolder.loanAmountStart, 1000);
  const loanAmountEndDebounce = useHookstateDebounce(paginationLoanHolder.loanAmountEnd, 1000);

  const pageSizeDebounceApplicants = useHookstateDebounce(paginationLoanApplicants.pageSize, 500);
  const pageDebounceApplicants = useHookstateDebounce(paginationLoanApplicants.page, 500);
  const startDateDebounceApplicants = useHookstateDebounce(paginationLoanApplicants.startDate, 500);
  const endDateDebounceApplicants = useHookstateDebounce(paginationLoanApplicants.endDate, 500);
  const requirements = useHookstateDebounce(paginationLoanApplicants.requirements, 500);
  const stage = useHookstateDebounce(paginationLoanApplicants.stage, 500);
  const isCompleted = useHookstateDebounce(paginationLoanApplicants.isCompleted, 500);
  const isWithdrawn = useHookstateDebounce(paginationLoanApplicants.isWithdrawn, 500);

  useEffect(() => {
    getGroups();
  }, []);

  useEffect(() => {
    Promise.all([getLoanHolders(), getLoanHoldersDashboard()]);
  }, [
    pageSizeDebounce,
    pageDebounce,
    creditScoreGroupIdsDebounce,
    loanTermDebounce,
    paymentStatusesDebounce,
    loanBalanceStartDebounce,
    loanBalanceEndDebounce,
    loanAmountStartDebounce,
    loanAmountEndDebounce,
    startDateDebounce,
    endDateDebounce,
  ]);

  useEffect(() => {
    Promise.all([getLoanApplicants(), getLoanApplicantsDashboard()]);
  }, [
    pageSizeDebounceApplicants,
    pageDebounceApplicants,
    startDateDebounceApplicants,
    endDateDebounceApplicants,
    stage,
    requirements,
    isCompleted,
    isWithdrawn,
  ]);

  return (
    <div className="p-6 lg:p-8">
      {tabLoanHolder.value === TabLoanHolderEnum.LOAN_HOLDER && (
        <LoanHolderTable
          columns={LoanHolderColumns}
          data={loanHolders.data.get({ noproxy: true })}
          metadata={loanHolders['meta'].get({ noproxy: true })}
        />
      )}
      {tabLoanHolder.value === TabLoanHolderEnum.LOAN_APPLICANTS && (
        <LoanHolderTable
          columns={LoanApplicantsColumns}
          data={loanApplicants.data.get({ noproxy: true })}
          metadata={loanApplicants['meta'].get({ noproxy: true })}
        />
      )}
    </div>
  );
}
