'use client';

import { Control, Controller, FieldErrors, FieldValues, UseFormRegister, UseFormWatch } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import MultipleSelector from '@/components/ui/multiple-selector';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

// Import constants from FRO
const YES_NO_OPTIONS = [
  { label: 'Yes', value: '1' },
  { label: 'No', value: '0' },
];

const COOPERATIVE_FARMER_ASSOC = [
  { label: 'Member', value: 'Member' },
  { label: 'Officer', value: 'Officer' },
];

const LOAN_SOURCES = [
  { label: 'Government Programs', value: 'Government Programs' },
  { label: 'Cooperative', value: 'Cooperative' },
  { label: 'Private Lenders', value: 'Private Lenders' },
  { label: 'Middle-men', value: 'Middle-men' },
];

interface IFarmBusinessInformationFormProps {
  register: UseFormRegister<FieldValues>;
  control: Control<FieldValues, any>;
  errors: FieldErrors<FieldValues>;
  watch: UseFormWatch<FieldValues>;
}

export default function FarmBusinessInformationForm({
  register,
  control,
  errors,
  watch,
}: IFarmBusinessInformationFormProps) {
  const gState = useGlobalState();
  const [isMemberOfOrganization, hasPastFarmLoans, hasNeedFarmLoan] = watch([
    'isMemberOfOrganization',
    'hasPastFarmLoans',
    'hasNeedFarmLoan',
  ]);

  return (
    <div>
      <FormTitle title="Farm Business Information" />
      <div className="mt-6 grid gap-4 space-y-4 sm:grid-cols-2 xl:grid-cols-3">
        <FormField
          name="isMemberOfOrganization"
          label="Member of a Cooperative or Farmers Association?"
          errors={errors}
        >
          <Controller
            control={control}
            name="isMemberOfOrganization"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.isMemberOfOrganization &&
                      'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {isMemberOfOrganization === '1' && (
          <>
            <FormField name="organizationName" label="Organization Name" errors={errors}>
              <Input
                {...register('organizationName')}
                className={cn(
                  'focus-visible:ring-primary',
                  errors.organizationName && 'border-red-500 focus-visible:ring-red-500',
                )}
                type="text"
                placeholder="Enter Organization Name"
                disabled={!gState.accountProfileIsEdit.value}
              />
            </FormField>

            <FormField name="organizationPosition" label="Position in Organization" errors={errors}>
              <Controller
                control={control}
                name="organizationPosition"
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={!gState.accountProfileIsEdit.value}
                  >
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.organizationPosition &&
                          'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Select Position" />
                    </SelectTrigger>
                    <SelectContent>
                      {COOPERATIVE_FARMER_ASSOC.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </FormField>
          </>
        )}

        <FormField name="hasPastFarmLoans" label="Do you have any past farm loans?" errors={errors}>
          <Controller
            control={control}
            name="hasPastFarmLoans"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.hasPastFarmLoans && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {hasPastFarmLoans === '1' && (
          <>
            <FormField name="pastFarmLoans" label="Past Farm Loans Source" errors={errors}>
              <Controller
                control={control}
                name="pastFarmLoans"
                render={({ field: { onChange, value } }) => (
                  <MultipleSelector
                    value={value}
                    onChange={onChange}
                    disabled={!gState.accountProfileIsEdit.value}
                    defaultOptions={LOAN_SOURCES}
                    placeholder="Select loan sources"
                    emptyIndicator={
                      <p className="text-center text-lg leading-10 text-gray-600 dark:text-gray-400">
                        No results found.
                      </p>
                    }
                  />
                )}
              />
            </FormField>

            <FormField name="hasPastFarmLoanPaid" label="Have you paid your past farm loans?" errors={errors}>
              <Controller
                control={control}
                name="hasPastFarmLoanPaid"
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={!gState.accountProfileIsEdit.value}
                  >
                    <SelectTrigger
                      className={cn(
                        'focus-visible:ring-primary',
                        errors.hasPastFarmLoanPaid &&
                          'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                      )}
                    >
                      <SelectValue placeholder="Select Answer" />
                    </SelectTrigger>
                    <SelectContent>
                      {YES_NO_OPTIONS.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
            </FormField>
          </>
        )}

        <FormField name="hasNeedFarmLoan" label="Do you need a farm loan?" errors={errors}>
          <Controller
            control={control}
            name="hasNeedFarmLoan"
            render={({ field }) => (
              <Select value={field.value} onValueChange={field.onChange} disabled={!gState.accountProfileIsEdit.value}>
                <SelectTrigger
                  className={cn(
                    'focus-visible:ring-primary',
                    errors.hasNeedFarmLoan && 'border-red-500 focus:ring-2 focus:ring-red-500 focus:ring-offset-2',
                  )}
                >
                  <SelectValue placeholder="Select Answer" />
                </SelectTrigger>
                <SelectContent>
                  {YES_NO_OPTIONS.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        {hasNeedFarmLoan === '1' && (
          <FormField name="needFarmLoanReason" label="Reason for needing farm loan" errors={errors}>
            <Input
              {...register('needFarmLoanReason')}
              className={cn(
                'focus-visible:ring-primary',
                errors.needFarmLoanReason && 'border-red-500 focus-visible:ring-red-500',
              )}
              type="text"
              placeholder="Enter reason for needing farm loan"
              disabled={!gState.accountProfileIsEdit.value}
            />
          </FormField>
        )}
      </div>
    </div>
  );
}
