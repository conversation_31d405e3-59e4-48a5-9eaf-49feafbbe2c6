'use client';

import { useGlobalStatePersist } from '@/lib/store/persist';

import LoanHoldersPage from './_components/loan-holders';
import NonLoanHoldersPage from './_components/non-loan-holders';
import AccountRequestPage from './_components/request';

export default function AdminAccounts() {
  const gStateP = useGlobalStatePersist();

  return (
    <div>
      {/* {gStateP.tabsAccounts.value === 'members' && <MembersPage />} */}
      {gStateP.tabsAccounts.value === 'nonloan' && <NonLoanHoldersPage />}
      {gStateP.tabsAccounts.value === 'loan' && <LoanHoldersPage />}
      {gStateP.tabsAccounts.value === 'request' && <AccountRequestPage />}
    </div>
  );
}
