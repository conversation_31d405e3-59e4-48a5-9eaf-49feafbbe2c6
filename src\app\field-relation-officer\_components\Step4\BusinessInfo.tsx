import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { useDisableNumberInputScroll } from '@/lib/hooks/utils/useDisableNumberInputScroll';

import { businessInfoSchema, TBusinessInfoSchema } from '../../schemas';
import { IBusinessInfo, IFROState, ISafeParseResult } from '../../types';
import FarmBusinessInformation from './FarmBusinessInformation';
import FinancialInformation from './FinancialInformation';
import PurchaserInformation from './PurchaserInformation';

interface IBusinessInfoProps {
  gStateFRO: State<IFROState, LocalStored>;
  onPrevious: () => void;
  onClear: () => void;
  onSave: () => void;
  isLoading: boolean;
}

const BusinessInfo = ({ gStateFRO, onPrevious, onClear, onSave, isLoading }: IBusinessInfoProps) => {
  useDisableNumberInputScroll();
  const data = gStateFRO?.form.step4?.value;
  const [onSubmitted, setOnSubmitted] = useState<boolean>(false);
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TBusinessInfoSchema>({
    resolver: zodResolver(businessInfoSchema),
    defaultValues: {
      // Financial Information
      sourceOfFunds: data?.sourceOfFunds ? data.sourceOfFunds.split(',').map((v) => v.trim()) : [],
      monthlyGrossIncome: data?.monthlyGrossIncome || 0,

      // Farm Business Information
      isMemberOfOrganization: data?.isMemberOfOrganization === '1' ? '1' : '0',
      organizationName: data?.organizationName || '',
      organizationPosition: data?.organizationPosition || '',
      hasPastFarmLoans: data?.hasPastFarmLoans === '1' ? '1' : '0',
      pastFarmLoans: data?.pastFarmLoans || '',
      hasPastFarmLoanPaid: data?.hasPastFarmLoanPaid === '1' ? '1' : '0',
      hasNeedFarmLoan: data?.hasNeedFarmLoan === '1' ? '1' : '0',
      needFarmLoanReason: data?.needFarmLoanReason || '',
      isInterestedToSellAtTradingPost: data?.isInterestedToSellAtTradingPost === '1' ? '1' : '0',

      // Purchaser Information
      purchaserSellingLocation: data?.purchaserSellingLocation || '',
      purchaserFullname: data?.purchaserFullname || '',
      purchaserContactNumber: data?.purchaserContactNumber || '',
    },
  });

  const handleNext = handleSubmit(() => {
    setOnSubmitted(true);
    onSave();
  });

  const handlePrevious = () => onPrevious();

  useEffect(() => {
    if (!onSubmitted) {
      const subscription = watch((value) => {
        // Convert form data to IBusinessInfo format
        const businessInfo: IBusinessInfo = {
          ...value,
          sourceOfFunds: Array.isArray(value.sourceOfFunds)
            ? value.sourceOfFunds.join(', ')
            : value.sourceOfFunds || '',
          sourceOfFundsOthers: '', // This field is handled internally by the component
        } as IBusinessInfo;

        gStateFRO.form.step4.set(businessInfo);
      });

      return () => subscription.unsubscribe();
    }
  }, [watch(), onSubmitted]);

  console.log('Step4:', { errors, values: values() });

  return (
    <form onSubmit={handleNext}>
      <FinancialInformation register={register} control={control} errors={errors} />
      <FarmBusinessInformation register={register} control={control} errors={errors} watch={watch} />
      <PurchaserInformation register={register} control={control} errors={errors} />
      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear} disabled={isLoading}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious} disabled={isLoading}>
            Previous
          </Button>
          <Button type="submit">{isLoading ? 'Submitting' : 'Submit'}</Button>
        </div>
      </div>
    </form>
  );
};

export default BusinessInfo;
