'use client';

import { Table } from '@tanstack/react-table';

import { Checkbox } from '@/components/ui/checkbox';

import { useGlobalState } from '@/lib/store';

import { RequirementProps } from '../landbank-reqts-types';

export const LandbankSelectAllHeader = ({ table }: { table: Table<RequirementProps> }) => {
  const gState = useGlobalState();
  const rows = table.getRowModel().rows;

  const attachments = rows.map((row) => row.original.attachment).filter(Boolean) as string[];

  const selectedAttachments = gState.landbankReqs.selectedAttachments.get();

  const allSelected = attachments.length > 0 && selectedAttachments.length === attachments.length;

  const someSelected = !allSelected && selectedAttachments.length > 0;

  const handleSelectAll = (checked: boolean | string) => {
    const isChecked = checked === true;

    if (isChecked) {
      const uniqueAttachments = Array.from(new Set([...selectedAttachments, ...attachments]));
      gState.landbankReqs.selectedAttachments.set(uniqueAttachments);

      rows.forEach((row) => {
        if (row.original.attachment) row.toggleSelected(true);
      });
    } else {
      const newSelections = selectedAttachments.filter((url) => !attachments.includes(url));
      gState.landbankReqs.selectedAttachments.set(newSelections);
      rows.forEach((row) => row.toggleSelected(false));
    }
  };

  return attachments.length > 0 ? (
    <Checkbox
      checked={allSelected}
      onCheckedChange={handleSelectAll}
      aria-label="Select all"
      className={someSelected ? 'bg-primary/50' : ''}
      aria-checked={someSelected ? 'mixed' : allSelected ? 'true' : 'false'}
    />
  ) : null;
};
