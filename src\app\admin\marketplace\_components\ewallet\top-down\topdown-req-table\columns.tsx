'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { RequestStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { cn } from '@/lib/utils';

export const columnsTopDownReq = [
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max uppercase">
          <Badge className={cn(RequestStatusLabels[data.status].color, 'capitalize')}>
            {RequestStatusLabels[data.status].label}
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => `${row.vehicle_plate_number}`,
  },
  {
    id: 'name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Name" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.user.farmer.first_name} {data.user.farmer.last_name}
        </div>
      );
    },
    accessorFn: (row) => {
      return `${row.user.farmer.first_name} ${row.user.farmer.last_name}`;
    },
  },
  {
    id: 'account_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account ID" />,
    accessorFn: (row) => {
      return `ID${row.user_id.toString().padStart(9, '0')}`;
    },
  },
  {
    id: 'amount_to_deduct',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Amount to Deduct" />,
    accessorFn: (row) => {
      return `${Number(row.amount).toLocaleString('en-US', {
        style: 'currency',
        currency: 'PHP',
      })}`;
    },
  },
  {
    id: 'reference_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Reference No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="text-sm font-medium">{data.reference_number}</div>;
    },
    accessorFn: (row) => {
      return `${row.reference_number}`;
    },
  },
  {
    id: 'date_request',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Requested" />,
    accessorFn: (row) => {
      return `${format(new Date(row.created_at), 'MMM dd, yyyy | hh:mm a')}`;
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    accessorFn: (row) => {
      const isAdmin = row.processedBy.admin;
      const isFinance = row.processedBy.finance;

      return `${isAdmin ? `${isAdmin.first_name} ${isAdmin.last_name}` : `${isFinance.first_name} ${isFinance.last_name}`}`;
    },
  },
];
