'use client';

import { useHookstate } from '@hookstate/core';
import { PaperclipIcon, Trash2Icon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { useForm } from 'react-hook-form';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

import useCreditScoreMgt from '@/lib/hooks/admin/useCreditScoreMgt';
import useFinance from '@/lib/hooks/admin/useFinance';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { useTopDownStore } from '@/lib/store/top-down-store';
import { cn, urlify } from '@/lib/utils';

export default function RequestPage() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const data = useHookstate(gStateP.selected['farmer']);
  const address = data.value && data['farmer']['address'].value ? JSON.parse(data['farmer']['address'].value) : {};

  const loading = useHookstate(false);
  const { requestTopDown } = useTopDownStore('admin');

  const [files, setFiles] = useState([]);
  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      'image/png': ['.png', '.jpeg', '.jpg', '.webp'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc', '.docx'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.doc', '.docx'],
    },
    onDrop: (acceptedFiles) => {
      const newFiles = acceptedFiles.filter((file) => !files.some((f) => f.name === file.name));
      setFiles((v) => [...v, ...newFiles]);
    },
    multiple: true,
  });

  const removeFile = (file) => {
    const newFiles = [...files];
    newFiles.splice(newFiles.indexOf(file), 1);
    setFiles(newFiles);
  };

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    defaultValues: {
      amount: '',
      remarks: '',
    },
  });

  const onSubmit = async (data: any) => {
    try {
      loading.set(true);

      const formData = {
        ...data,
        userId: gStateP.selected['farmer']['id'].value,
        documents: files,
      };

      console.log('onSubmit: ', formData);
      await requestTopDown(formData);

      loading.set(false);
    } catch (e) {
      console.error(e);
    } finally {
      loading.set(false);
    }
  };

  return (
    <div className="p-6 md:p-8">
      <div className="card">
        <div className="flex gap-8">
          <div>
            {/* Profile Image */}
            <div>
              <img
                className="mx-auto size-40 rounded-full border bg-white ring ring-white"
                src={data.user_img.value ? urlify(data.user_img.value, 'users/profile') : '/assets/user-default.jpg'}
                alt=""
              />
            </div>

            <div className="mt-4 flex justify-center">
              <Button
                onClick={() => {
                  router.push(`/admin/account-info/?id=${data['farmer'].user_id.value}`);
                }}
              >
                View more info
              </Button>
            </div>
          </div>

          <div className="flex-1">
            <div className="text-xl font-bold leading-loose text-indigo-900">Account Information</div>
            <dl className="grid grid-cols-2 gap-4">
              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Name</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.first_name.value} ${data.farmer.last_name.value}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Account ID</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`ID${data.id.value.toString().padStart(9, '0')}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Contact No.</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${data.farmer.mobile_number.value ?? ''}`}
                  </dd>
                </div>
              </div>

              <div className="font-dmSans">
                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Address</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${address.addressHouseNumber ?? ''} 
              ${address.addressBarangay ? JSON.parse(address.addressBarangay)?.brgy_name : ''} 
              ${address.addressCity ? JSON.parse(address.addressCity)?.city_name : ''} 
              ${address.addressProvince ? JSON.parse(address.addressProvince)?.province_name : ''} 
              ${address.addressZipCode || ''}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Birthdate</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">
                    {`${new Date(data.farmer.birth_date.value).toLocaleString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                    })}`}
                  </dd>
                </div>

                <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-slate-400">Email</dt>
                  <dd className="mt-1 font-medium leading-6 text-primary sm:col-span-2 sm:mt-0">{`${data.email.value}`}</dd>
                </div>
              </div>
            </dl>
          </div>
        </div>
      </div>

      <div className="grid gap-4 pt-8 sm:grid-cols-2 2xl:grid-cols-3">
        <div className="card space-y-2 text-center">
          <div className="text-2xl font-bold text-primary">
            {data.value
              ? Number(data.wallet.value ? data.wallet.balance.value : 0).toLocaleString('en-US', {
                  style: 'currency',
                  currency: 'PHP',
                })
              : 0}
          </div>
          <div className="text-sm font-bold text-[#A3AED0]">E-Wallet Load</div>
        </div>
      </div>

      <form id="topdown-form" className="mt-8 grid grid-cols-2 gap-8" onSubmit={handleSubmit(onSubmit)}>
        <div>
          <div className="text-xl font-bold leading-loose text-kitaph-primary">E-wallet Deduction Request Details</div>
          <div className="my-6 grid gap-6">
            {/* Amount to Deduct */}
            <div className="grid gap-3">
              <Label htmlFor="amount">
                Amount to Deduct <strong className="text-red-500">*</strong>
              </Label>
              <Input
                {...register('amount', {
                  required: 'Please fill in all required fields before submitting.',
                  validate: {
                    isPositive: (v) => Number(v) > 0 || 'Amount must be greater than 0',
                  },
                })}
                className={cn(errors.amount && 'border-red-500 focus-visible:ring-red-500')}
                type="number"
                min={0.01}
                step={0.01}
                placeholder="e.g ₱1000.00"
              />
              {errors.amount && <p className="form-error">{`${errors.amount.message}`}</p>}
            </div>

            <div>
              <div className="text-xl font-bold leading-loose text-kitaph-primary">
                Remarks<strong className="text-red-500">*</strong>
              </div>

              <div className="grid gap-3">
                {/* <Label htmlFor="remarks">Remarks</Label> */}
                <Textarea
                  {...register('remarks', {
                    required: 'Please fill in all required fields before submitting.',
                  })}
                  className={cn(errors.remarks && 'border-red-500 focus-visible:ring-red-500')}
                  placeholder="Enter remarks here"
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>
          </div>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-kitaph-primary">
                Attachments<span className="text-red-500">*</span>
              </CardTitle>
              <CardDescription>Attach supporting files (e.g., documents, receipts).</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6">
                {/* Image Dropzone */}
                <div className="pt-2">
                  <div className="border-spacing-4 cursor-pointer rounded-lg border-2 border-dashed border-gray-300 p-6 text-center">
                    <div {...getRootProps({ className: 'dropzone' })}>
                      <input {...getInputProps()} />

                      {/* eslint-disable-next-line jsx-a11y/alt-text */}
                      <PaperclipIcon className="mx-auto mb-2 size-12 text-gray-500" />
                      <p>{`Drag 'n' drop files here, or`}</p>

                      <div className="py-4 font-bold text-primary">Browse</div>
                    </div>
                  </div>
                </div>

                {files.length > 0 && (
                  <div className="grid gap-2">
                    {files.map((file) => {
                      return (
                        <div
                          key={file.name}
                          className="flex items-center justify-between rounded-md border border-green-500 px-4 py-1"
                        >
                          <div>{file.name}</div>

                          <Button
                            className="size-8 rounded-full bg-red-50 hover:bg-red-100"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFile(file)}
                          >
                            <Trash2Icon className="size-4 text-red-500" />
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </form>

      <div className="mt-6 flex justify-end gap-4">
        <Button variant="outline" onClick={() => router.push('/admin/marketplace')}>
          Cancel
        </Button>
        {loading.value ? (
          <ButtonLoading />
        ) : (
          <Button type="submit" form="topdown-form">
            Submit Request
          </Button>
        )}
      </div>
    </div>
  );
}
