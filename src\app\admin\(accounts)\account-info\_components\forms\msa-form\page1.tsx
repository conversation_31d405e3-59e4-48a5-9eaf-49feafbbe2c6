'use client';

import React from 'react';

import { FormDimensions } from '@/lib/constants/enums';

interface PersonData {
  first_name: string;
  middle_name?: string;
  last_name: string;
  address?: string;
}

interface Page1Props {
  data: PersonData;
}

const Page1: React.FC<Page1Props> = ({ data }) => {
  const address = data && data['address'] ? JSON.parse(data['address']) : {};

  const now = new Date();
  const day = now.getDate();
  const month = now.toLocaleString('default', { month: 'long' });
  const year = now.getFullYear();

  return (
    <div className={`relative flex flex-col border bg-white p-20 print:border-none ${FormDimensions.LEGAL}`}>
      {/* Header */}
      <div className="mb-8 text-center">
        <h1 className="text-lg font-bold uppercase">KASUNDUAN SA PAMAMAHALA</h1>
        <h2 className="text-base font-medium uppercase">MANAGEMENT SERVICES AGREEMENT (MSA)</h2>
      </div>

      {/* Opening paragraph */}
      <div className="mb-6 text-sm">
        <span className="font-bold">Ang Kasunduang ito</span> ay ginawa at napagkasunduan noong ika-
        <span className="mx-1 inline-block w-4 border-b border-black text-center">{day}</span>
        <span> araw ng </span>
        <span className="mx-1 inline-block w-24 border-b border-black text-center">{month + ', ' + year}</span>
        <span>, sa pagitan ng:</span>
      </div>

      {/* KITA AGRITECH CORP Section */}
      <div className="mb-8 ml-20 text-sm">
        <p className="mb-2">
          <span className="font-bold">KITA AGRITECH CORP.</span> (tinutukoy sa dokumentong ito bilang
        </p>
        <p className="mb-2">&ldquo;KITA&rdquo;), isang korporasyon na wastong itininatag at alinsunod sa mga</p>
        <p className="mb-2">batas ng Republika ng Pilipinas, na may punong tanggapan sa #86</p>
        <p className="mb-2">Lorraine St., Apolonio Samson, Balintawak, Quezon City, at</p>
        <p className="mb-2">kinakatawan ng kanyang opisyal na kinatawan, G. Earwin A. Belen,</p>
        <p className="mb-4">MSc., R.Agr.;</p>
      </div>

      {/* AT Section */}
      <div className="mb-6 text-center text-sm font-bold">- AT -</div>

      {/* Partner-Farmer Section */}
      <div className="mb-8 ml-20 text-sm">
        <div className="mb-2">
          <span>ni</span>
          <span className="mx-4 inline-block w-64 border-b border-black text-center">
            {`${data?.first_name || ''} ${data?.middle_name || ''} ${data?.last_name || ''}`.trim()}
          </span>
          <span> (tinutukoy sa dokumentong ito bilang</span>
        </div>
        <span className="leading-6">
          &quot;Partner-Farmer&quot;), nasa tamang edad, at naninirahan sa
          <span className="mx-2 w-fit border-b border-black px-6 text-center">
            {address?.addressHouseNumber && address?.addressBarangay && address?.addressCity && address?.addressProvince
              ? `${address.addressHouseNumber}, ${JSON.parse(address.addressBarangay)?.brgy_name || ''}, ${JSON.parse(address.addressCity)?.city_name || ''}, ${JSON.parse(address.addressProvince)?.province_name || ''}`
              : ''}
          </span>
        </span>
      </div>

      {/* Section 1 - Layunin */}
      <div className="mb-6 text-sm">
        <h3 className="mb-2 font-bold">1. Layunin</h3>
        <p className="text-justify">
          Isinasaad ng Kasunduang ito ang mga serbisyo ng pamamahala na ibibigay ng KITA upang mapabuti ang operasyon ng
          sakahan, pangangasiwa ng pananalapi, at pagsunod sa mga kinakailangan ng Agrisenso Plus Lending Program ng
          LANDBANK.
        </p>
      </div>

      {/* Section 2 - Mga Saklaw na Serbisyo */}
      <div className="mb-6 text-sm">
        <h3 className="mb-2 font-bold">2. Mga Saklaw na Serbisyo</h3>
        <p className="mb-2">Ang mga sumusunod ay ang mga serbisyong ibinibigay ng KITA:</p>

        <div className="ml-4 space-y-2">
          <p>
            a. <span className="underline"> Farm Operations Management:</span> Pagtulong at pagbantay sa pang-araw-araw
            na gawain sa sakahan, katulad ng paglilinang ng lupa, pagtatanim, at pag-aani.
          </p>

          <p>
            b. <span className="underline">Financial Management:</span> Pamamahala at pagbabadyet ng Loan Proceeds mula
            sa Lending Program, pagbabantay sa mga gastos, at paggawa ng mga Financial Reports para sa loan monitoring
          </p>

          <p>
            c. <span className="underline">Compliance and Reporting:</span> Siguraduhin na lahat ng mga dokumentong may
            kaugnayan sa pagkakautang at mga kinakailangan sa pagre-report ay nagawa ng Partner-Farmer.
          </p>

          <p>
            d. <span className="underline">Data Collection and Analysis:</span> Mangolekta ng mga datos at impormasyon
            mula sa sakahan upang ito ay magamit upang mapaganda ang produksyon.
          </p>
        </div>
      </div>

      {/* Section 3 - Mga Obligasyon ng Partner-Farmer */}
      <div className="mb-6 text-sm">
        <h3 className="mb-2 font-bold">3. Mga Obligasyon ng Partner-Farmer</h3>
        <p className="mb-2">Ang Partner-Farmer ay:</p>

        <div className="ml-4">
          <p>a. Pinahihintulutang pumunta ang KITA sa sakahan upang ibigay ang mga serbisyong nakalathala sa itaas.</p>
        </div>
      </div>
    </div>
  );
};

export default Page1;
