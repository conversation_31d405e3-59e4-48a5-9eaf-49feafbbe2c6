'use client';

import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { PaymentStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { cn } from '@/lib/utils';

export const columns = [
  {
    id: 'payment_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest ? format(new Date(data.loanPaymentRequest.payment_date), 'MMM dd, yyyy') : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return data.loanPaymentRequest
        ? `${format(new Date(data.loanPaymentRequest.payment_date), 'MMM dd, yyyy')}`
        : '-';
    },
  },
  {
    id: 'date_processed',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Processed" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest
            ? format(new Date(data.loanPaymentRequest.created_at), 'MMM dd, yyyy | hh:mm a')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return data.loanPaymentRequest
        ? format(new Date(data.loanPaymentRequest.created_at), 'MMM dd, yyyy | hh:mm a')
        : '-';
    },
  },
  {
    id: 'due_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Due Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest
            ? format(new Date(data.loanPaymentRequest?.topupRequest?.due_at), 'MMM dd, yyyy')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return data.loanPaymentRequest
        ? format(new Date(data.loanPaymentRequest?.topupRequest?.due_at), 'MMM dd, yyyy')
        : '-';
    },
  },
  {
    id: 'loan_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest ? `${data.loanPaymentRequest?.topupRequest?.reference_number}` : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return data.loanPaymentRequest ? `${data.loanPaymentRequest?.topupRequest?.reference_number}` : '-';
    },
  },
  {
    id: 'payment_id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment ID" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest ? `${data.loanPaymentRequest?.reference_number}` : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return data.loanPaymentRequest ? `${data.loanPaymentRequest?.reference_number}` : '-';
    },
  },
  {
    id: 'payment_amount',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Amount" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.amount).toLocaleString('en-US', {
          style: 'currency',
          currency: 'PHP',
        })}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row.amount}`;
    },
  },
  {
    id: 'remaining_bal',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Remaining Balance" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPaymentRequest
            ? `${Number(data.loanPaymentRequest?.remaining_balance ?? 0).toLocaleString('en-US', {
                style: 'currency',
                currency: 'PHP',
              })}`
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      return row.loanPaymentRequest ? `${row.loanPaymentRequest?.remaining_balance ?? 0}` : '-';
    },
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <>
          {data.loanPaymentRequest ? (
            <Badge className={cn('min-w-max', PaymentStatusLabels[data.loanPaymentRequest?.status].color)}>
              {PaymentStatusLabels[data.loanPaymentRequest?.status].label}
            </Badge>
          ) : (
            'N/A'
          )}
        </>
      );
    },
    accessorFn: (row) => {
      return row.loanPaymentRequest ? `${PaymentStatusLabels[row.loanPaymentRequest?.status].label}` : '-';
    },
  },
  {
    id: 'processed_by',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Processed By" />,
    accessorFn: (row) => {
      const processedBy = row?.loanPaymentRequest?.processedBy ?? {};
      return `${processedBy.finance ? `${processedBy.finance.first_name} ${processedBy.finance.last_name}` : processedBy.email}`;
    },
  },
];
