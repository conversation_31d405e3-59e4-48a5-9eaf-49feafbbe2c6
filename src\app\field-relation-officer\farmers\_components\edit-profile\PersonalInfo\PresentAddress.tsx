import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';
import { Control, Controller, FieldErrors, UseFormGetValues, UseFormRegister, UseFormSetValue } from 'react-hook-form';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { TPersonalInformationSchema } from '@/app/field-relation-officer/schemas';
import { safeJson } from '@/app/field-relation-officer/utils';
import { useDisableNumberInputScroll } from '@/lib/hooks/utils/useDisableNumberInputScroll';
import useSelectAddress from '@/lib/hooks/utils/useSelectAddress';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

interface IPresentAddressProps {
  register: UseFormRegister<TPersonalInformationSchema>;
  control: Control<TPersonalInformationSchema>;
  errors: FieldErrors<TPersonalInformationSchema>;
  values: UseFormGetValues<TPersonalInformationSchema>;
  setValue: UseFormSetValue<TPersonalInformationSchema>;
}

const PresentAddress = ({ register, control, errors, values, setValue }: IPresentAddressProps) => {
  useDisableNumberInputScroll();
  const gStateP = useGlobalStatePersist();
  const address = useSelectAddress();
  const regions = useHookstate(gStateP.fro.options.address.regions).value;

  const {
    setStreet,
    setRegionSelected,
    setProvinceSelected,
    provinceList,
    setCitySelected,
    cityList,
    setBarangaySelected,
    barangayList,
    setPostalCode,
    regionSelected,
    provinceSelected,
    getRegionByCode,
  } = address;

  useEffect(() => {
    if (values().addressProvince) {
      setProvinceSelected(safeJson(values().addressProvince));
    }
    if (values().addressCity) {
      setCitySelected(safeJson(values().addressCity));
    }
    if (values().addressBarangay) {
      setBarangaySelected(safeJson(values().addressBarangay));
    }
  }, [values().addressProvince, values().addressCity, values().addressBarangay]);

  useEffect(() => {
    if (provinceSelected && values().addressProvince) {
      const _province = safeJson(values().addressProvince);
      const _region = getRegionByCode(_province.region_code);
      setRegionSelected(_region);
    }
  }, [provinceSelected]);

  useEffect(() => {
    if (regionSelected) {
      setValue('addressRegion', JSON.stringify(regionSelected));
    }
  }, [regionSelected]);

  return (
    <div className="mt-6">
      <FormTitle title="Present Address" />
      <div className="grid gap-x-4 gap-y-8 sm:grid-cols-2 xl:grid-cols-3">
        <FormField name="addressHouseNumber" label="House No./Lot/Bldg/Purok" errors={errors} required>
          <Controller
            control={control}
            name="addressHouseNumber"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter House No."
                onChange={(e) => {
                  field.onChange(e.target.value);
                  setStreet(e.target.value);
                }}
                className={cn('focus-visible:ring-primary', errors.addressHouseNumber && 'border-red-500')}
              />
            )}
          />
        </FormField>

        <FormField name="addressStreet" label="Street/Sitio/Subdivision" errors={errors} required>
          <Input
            {...register('addressStreet', { required: 'Required' })}
            placeholder="Enter Street"
            className={cn(errors.addressStreet && 'border-red-500')}
          />
        </FormField>

        <FormField name="addressRegion" label="Region" errors={errors} required>
          <Controller
            control={control}
            name="addressRegion"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) setRegionSelected(JSON.parse(v));
                }}
                value={field.value}
              >
                <SelectTrigger className={cn(errors.addressRegion && 'border-red-500')}>
                  <SelectValue placeholder="Select region" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {regions.map((region: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(region)}>
                        {region.region_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="addressProvince" label="Province" errors={errors} required>
          <Controller
            control={control}
            name="addressProvince"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) setProvinceSelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!provinceList.length}
              >
                <SelectTrigger className={cn(errors.addressProvince && 'border-red-500')}>
                  <SelectValue placeholder="Select province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {provinceList.map((prov: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(prov)}>
                        {prov.province_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="addressCity" label="City/Municipality" errors={errors} required>
          <Controller
            control={control}
            name="addressCity"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) setCitySelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!cityList.length}
              >
                <SelectTrigger className={cn(errors.addressCity && 'border-red-500')}>
                  <SelectValue placeholder="Select city" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {cityList.map((city: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(city)}>
                        {city.city_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="addressBarangay" label="Barangay" errors={errors} required>
          <Controller
            control={control}
            name="addressBarangay"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select
                onValueChange={(v) => {
                  field.onChange(v);
                  if (v) setBarangaySelected(JSON.parse(v));
                }}
                value={field.value}
                disabled={!barangayList.length}
              >
                <SelectTrigger className={cn(errors.addressBarangay && 'border-red-500')}>
                  <SelectValue placeholder="Select Barangay" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {barangayList.map((brgy: any, idx) => (
                      <SelectItem key={idx} value={JSON.stringify(brgy)}>
                        {brgy.brgy_name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>

        <FormField name="addressZipCode" label="Zip Code" errors={errors}>
          <Controller
            control={control}
            name="addressZipCode"
            rules={{
              pattern: {
                value: /^\d{4}$/,
                message: 'Must be 4 digits (e.g., 1234)',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                placeholder="Enter Zip Code"
                onChange={(e) => {
                  field.onChange(e.target.value);
                  setPostalCode(e.target.value);
                }}
                className={cn(errors.addressZipCode && 'border-red-500')}
                onlyDigits
                inputMode="numeric"
                maxLength={4}
                type="text"
              />
            )}
          />
        </FormField>

        <FormField name="addressLengthOfStay" label="No. of Years Residing" errors={errors}>
          <Input
            {...register('addressLengthOfStay', {
              validate: {
                isPositive: (v) => v === '' || Number(v) >= 0 || 'Must be greater than or equal to 0',
              },
            })}
            type="number"
            min={0}
            placeholder="e.g., 10 years"
            className={cn(errors.addressLengthOfStay && 'border-red-500')}
          />
        </FormField>

        <FormField name="residenceOwnership" label="Home Ownership" errors={errors} required>
          <Controller
            control={control}
            name="residenceOwnership"
            rules={{ required: 'Required' }}
            render={({ field }) => (
              <Select onValueChange={field.onChange} value={field.value}>
                <SelectTrigger className={cn(errors.residenceOwnership && 'border-red-500')}>
                  <SelectValue placeholder="Select ownership" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="OWNED">Owned</SelectItem>
                    <SelectItem value="RENTED">Rented</SelectItem>
                    <SelectItem value="LIVING WITH RELATIVES">Living with relatives</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            )}
          />
        </FormField>
      </div>
    </div>
  );
};

export default PresentAddress;
