import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import { RotateCcw } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { FieldErrors, useForm } from 'react-hook-form';
import SignatureCanvas from 'react-signature-canvas';
import Webcam from 'react-webcam';

import { FormField } from '@/components/common/forms/form-field';
import FormTitle from '@/components/common/forms/form-title';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';

import { base64toFile, cn } from '@/lib/utils';

import { dataPrivacySchema, TDataPrivacySchema } from '../../schemas';
import { IDataPrivacy, IFROState, ISafeParseResult } from '../../types';

const videoConstraints = {
  width: 1280,
  height: 1208,
  facingMode: 'environment',
};

interface IDataPrivacyProps {
  onNext: () => void;
  gStateFRO: State<IFROState, LocalStored>;
  onClear: () => void;
  fromPreviousStep?: boolean;
}

const DataPrivacy = ({ onNext, gStateFRO, onClear, fromPreviousStep = false }: IDataPrivacyProps) => {
  const sigCanvasRef = useRef<SignatureCanvas>(null);
  const data = gStateFRO?.form.step1?.value;

  const createFileList = (file: File): FileList => {
    const dt = new DataTransfer();
    dt.items.add(file);
    return dt.files;
  };

  console.log('From Previous', fromPreviousStep);

  const {
    register,
    handleSubmit,
    formState: { errors },
    getValues: values,
    setValue,
    watch,
  } = useForm<TDataPrivacySchema>({
    resolver: zodResolver(dataPrivacySchema),
    defaultValues: {
      isAgreeUsingData: !!(data && data.isAgreeUsingData === 1),
      isAgreeVisitingFarm: !!(data && data.isAgreeVisitingFarm === 1),
      isAgreeSharingData: !!(data && data.isAgreeSharingData === 1),
      userImage:
        data?.userImage instanceof FileList
          ? data.userImage
          : data?.userImage instanceof File
            ? createFileList(data.userImage)
            : null,
      signature: (data?.signature as File) || null,
    },
  });

  console.log(
    'rendered data',
    data?.userImage instanceof FileList ? data.userImage[0] : (data?.userImage as File | null) || null,
    values(),
  );

  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);

  const handleSignatureEnd = async () => {
    const base64 = sigCanvasRef.current?.toDataURL('image/png');
    const signatureFile = await base64toFile(base64, 'signature.png');
    setValue('signature', signatureFile);
    setSignaturePreview(base64);
  };

  const handleClearSignature = () => {
    sigCanvasRef.current?.clear();
    setValue('signature', null);
    setSignaturePreview(null);
  };

  const handleNext = handleSubmit(() => onNext());

  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      const { data } = dataPrivacySchema.safeParse(values()) as ISafeParseResult<IDataPrivacy>;
      gStateFRO.form.step1.set(data);
    });

    return () => subscription.unsubscribe();
  }, [watch()]);

  console.log('Step1:', { errors, values: values() });
  const webcamRef = useRef(null);
  const [image, setImage] = useState(null);
  const [file, setFile] = useState(null);
  const [openCamera, setOpenCamera] = useState(false);
  const [showGuide, setShowGuide] = useState(true);

  const dataURLtoFile = (dataUrl: string, filename: string): File => {
    const arr = dataUrl.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, { type: mime });
  };

  const capture = () => {
    const imgSrc = webcamRef.current?.getScreenshot();
    if (!imgSrc) return;

    setImage(imgSrc);

    const file = dataURLtoFile(imgSrc, `farmer-capture-${Date.now()}.jpg`);
    console.log('File:', file);
    setFile(file);

    setValue('userImage', createFileList(file));
  };

  useEffect(() => {
    // Set signature preview from existing data when coming from previous step
    if (fromPreviousStep && data?.signature instanceof File) {
      const signatureUrl = URL.createObjectURL(data.signature);
      setSignaturePreview(signatureUrl);
    }
  }, [fromPreviousStep, data?.signature]);

  return (
    <div>
      <form onSubmit={handleNext}>
        <div className="flex flex-col space-y-4">
          <ScrollArea
            className={cn('flex h-[280px] flex-col rounded-md border p-4', errors.isAgreeUsingData && 'border-red-500')}
          >
            <div className="flex flex-col space-y-5">
              <FormTitle title="Consent Declaration and Data Privacy Policy Statement" />
              <div className="text-sm">
                Sumasang-ayon ako na maaaring gamitin ng{' '}
                <strong>KITA AGRITECH CORPORATION (&quot;KITA AGRITECH&quot;)</strong> ang impormasyong ito para sa
                pagpaparehistro ko sa Farmer Kita Program, alinsunod sa Data Privacy Policy Statement na nasa ibaba.
              </div>

              <div className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na bumisita sa aking sakahan at ako ay magbibigay ng
                karagdagang mga dokumento, at pahintulot sa pagkuha ng mga larawan at bidyo, upang beripikahin ang
                impormasyong aking inilagay sa form na ito.
              </div>

              <div className="text-sm">
                Pinapayagan ko ang <strong>KITA AGRITECH</strong> na ibahagi ang mga impormasyong aking ibinigay sa mga
                Partner Financial Institutions nito kung sakali man na ako ay mag-apply para sa Farm Production Loan
                bilang bahagi ng KYC procedures nito.
              </div>

              <div className="text-sm">
                Pinahihintulutan ko ang Kita Agritech Corp. na magsagawa ng reference checks at background verification
                bilang bahagi ng proseso.
              </div>

              <div className="text-sm">
                Pinapayagan ko ang Kita Agritech Corp. na gamitin ang aking personal na impormasyon para sa layunin ng
                pagpapadala ng mga marketing materials, promotional content, at iba pang anunsyo na may kaugnayan sa
                kanilang mga produkto at serbisyo.
              </div>

              <div className="text-sm">
                Pinatutunayan ko na ang lahat ng impormasyong aking isinulat sa application na ito ay totoo at kumpleto
                ayon sa aking kaalaman. Nauunawaan ko na ang anumang maling o mapanlinlang na impormasyon ay maaaring
                magresulta sa pagkadiskwalipika sa aking application.
              </div>

              <div className="text-sm">
                Nauunawaan ko na maaari kong bawiin ang aking pahintulot sa paggamit ng aking personal na datos anumang
                oras sa pamamagitan ng pormal na pakikipag-ugnayan sa Kita Agritech Corp.
              </div>

              <div className="flex flex-col gap-2">
                {(errors.isAgreeUsingData || errors.isAgreeVisitingFarm || errors.isAgreeSharingData) && (
                  <p className="text-sm text-red-500">Please check this box if you want to proceed</p>
                )}
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    {...register('isAgreeUsingData', { required: true })}
                    id="isAgreeUsingData"
                    onChange={(e) => {
                      const checked = e.target.checked;
                      setValue('isAgreeUsingData', checked);
                      setValue('isAgreeVisitingFarm', checked);
                      setValue('isAgreeSharingData', checked);
                    }}
                  />
                  <p className="text-sm">
                    Nabasa at naiintindihan ko ang mga Tuntunin at Kundisyon ng prosesong ito ng aplikasyon at ang Data
                    Privacy Statement, at ako ay sumasang-ayon dito.
                  </p>
                </div>
              </div>
            </div>
          </ScrollArea>

          <div className="flex flex-col gap-4 lg:flex-row">
            <Card className="lg:w-2/3">
              <CardContent className="space-y-4 p-4">
                <div className="relative rounded-md border border-gray-300">
                  <RotateCcw onClick={handleClearSignature} className="absolute bottom-2 right-2 cursor-pointer" />

                  {fromPreviousStep && (signaturePreview || data?.signature) ? (
                    <img
                      src={
                        signaturePreview || (data?.signature instanceof File ? URL.createObjectURL(data.signature) : '')
                      }
                      alt="Signature Preview"
                      className="h-40 w-auto rounded lg:h-52"
                    />
                  ) : (
                    <SignatureCanvas
                      ref={sigCanvasRef}
                      penColor="black"
                      canvasProps={{ className: 'w-full h-40 lg:h-52 rounded-md' }}
                      onEnd={handleSignatureEnd}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="p-4 lg:w-1/3">
              <div className="flex flex-1 flex-col items-center justify-center rounded-md border-2 border-dashed border-[#828282]/80 p-4 md:h-40 lg:h-52">
                <Dialog
                  open={openCamera}
                  onOpenChange={(open) => {
                    if (!open) {
                      setImage(null);
                      setFile(null);
                      setShowGuide(true);
                    }
                    setOpenCamera(open);
                  }}
                >
                  <DialogTrigger asChild>
                    <div className="flex cursor-pointer items-center justify-center text-left text-sm text-[#444A6D]">
                      <div className="float-right">
                        <p className="mb-2 text-xs font-medium">
                          Take a clear photo of the farmer holding a valid ID card near their chest, like in the sample
                          image.
                        </p>
                        <p className="mb-4 text-xs font-medium">
                          This shows they <strong>agree</strong> to the <strong>Data Privacy Policy</strong>.
                        </p>
                        <p className="mt-4 text-base font-bold">Take Photo</p>
                      </div>
                      <img className="h-[150px] xl:h-[180px]" src="/assets/illustrations/privacy.png" alt="" />
                    </div>
                  </DialogTrigger>
                  <DialogContent isXEnable={false}>
                    {showGuide ? (
                      <div className="flex flex-col items-center justify-center space-y-4 p-4">
                        <h3 className="text-center text-base font-semibold">
                          Take a clear photo of the farmer holding a valid ID card near their chest, like in the sample
                          image.
                        </h3>
                        <img src="/assets/illustrations/privacy.png" alt="" />

                        <Button onClick={() => setShowGuide(false)} className="w-full">
                          Continue
                        </Button>
                      </div>
                    ) : !image ? (
                      <>
                        <Webcam
                          audio={false}
                          ref={webcamRef}
                          screenshotFormat="image/jpeg"
                          videoConstraints={videoConstraints}
                        />
                        <Button onClick={capture}>Capture Selfie</Button>
                      </>
                    ) : (
                      <div>
                        <img src={image} alt="Captured ID" />
                        <div className="mt-4 flex gap-2">
                          <Button
                            onClick={() => {
                              setImage(null);
                              setFile(null);
                            }}
                            variant="outline"
                            className="flex-1"
                          >
                            Retake
                          </Button>
                          <Button
                            onClick={() => {
                              if (file) {
                                setValue('userImage', createFileList(file));
                              }
                              setOpenCamera(false);
                            }}
                            className="flex-1"
                          >
                            Use Photo
                          </Button>
                        </div>
                      </div>
                    )}
                  </DialogContent>
                </Dialog>
              </div>
            </Card>
          </div>

          <div className="flex justify-end gap-4">
            <Button type="submit" className="px-10">
              Next
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default DataPrivacy;
