'use client';

import { CircleCheckIcon, CircleXIcon } from 'lucide-react';

interface IItemListProps {
  isComplete: boolean;
  description: string;
}

export default function ItemList(props: IItemListProps) {
  return (
    <div className="flex items-start gap-2">
      {props.isComplete ? (
        <CircleCheckIcon className="size-5 shrink-0 text-green-500" />
      ) : (
        <CircleXIcon className="size-5 shrink-0 text-orange-500" />
      )}
      <div className="pt-px">{props.description}</div>
    </div>
  );
}
