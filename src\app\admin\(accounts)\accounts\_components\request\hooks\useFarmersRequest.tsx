'use client';

import { useHookstate } from '@hookstate/core';
import { keepPreviousData, useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import axios from '@/lib/api';
import { getUserType } from '@/lib/constants';
import useHookstateDebounce from '@/lib/hooks/utils/useHookstateDebounce';
import { useGlobalState } from '@/lib/store';
import { useGlobalStatePersist } from '@/lib/store/persist';

// Types
interface IFarmerRequest {
  id: number;
  email: string;
  status: number;
  user_type: number;
  created_at: string;
  farmer: {
    first_name: string;
    last_name: string;
    mobile_number: string;
  };
}

interface IFarmersRequestResponse {
  data: IFarmerRequest[];
  meta: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
    first_page_url: string;
    last_page_url: string;
    next_page_url: string | null;
    previous_page_url: string | null;
  };
}

// Fetcher function
const fetchFarmersRequest = async (params: {
  page: number;
  pageSize: number;
  search: string;
  status: string[];
  userType: string;
}): Promise<IFarmersRequestResponse> => {
  const { data } = await axios.get(`/${params.userType}/user/farmers/viewAllPaginated`, {
    params: {
      page: params.page,
      pageSize: params.pageSize,
      search: params.search,
      status: params.status,
    },
  });
  return data.data;
};

// Action functions
const approveFarmer = async (params: { userId: number; userType: string }) => {
  const { data } = await axios.post(`/${params.userType}/user/farmer/approve`, { userId: params.userId });
  return data.data;
};

const rejectFarmer = async (params: { userId: number; userType: string }) => {
  const { data } = await axios.post(`/${params.userType}/user/farmer/reject`, { userId: params.userId });
  return data.data;
};

// Hook
export const useFarmersRequest = () => {
  const gState = useGlobalState();
  const gStateP = useGlobalStatePersist();
  const paginationState = useHookstate(gState.admin.pagination.usersBulk);
  const queryClient = useQueryClient();

  // Get user type for API endpoint
  const userType = getUserType(gStateP.user.user.user_type.value);

  // Debounce search to avoid too many API calls
  const searchDebounce = useHookstateDebounce(paginationState.search, 500);

  const farmersRequestQuery = useQuery({
    // eslint-disable-next-line @tanstack/query/exhaustive-deps
    queryKey: [
      'farmersRequest',
      {
        page: paginationState.page.value,
        pageSize: paginationState.pageSize.value,
        search: searchDebounce.value as string,
        status: [...paginationState.status.get({ noproxy: true })],
        userType,
      },
    ],
    queryFn: () =>
      fetchFarmersRequest({
        page: paginationState.page.value,
        pageSize: paginationState.pageSize.value,
        search: searchDebounce.value as string,
        status: [...paginationState.status.get({ noproxy: true })],
        userType,
      }),
    placeholderData: keepPreviousData,
    refetchOnWindowFocus: false,
  });

  // Approve farmer mutation
  const approveFarmerMutation = useMutation({
    mutationFn: (userId: number) => approveFarmer({ userId, userType }),
    onMutate: () => {
      toast.loading('Approving Account...', {
        description: 'Please wait...',
        duration: 90000,
      });
    },
    onSuccess: () => {
      toast.dismiss();
      toast.success('Success', {
        description: 'Account approved successfully',
      });
      // Invalidate and refetch the farmers request query
      queryClient.invalidateQueries({ queryKey: ['farmersRequest'] });
    },
    onError: (error: any) => {
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error?.response?.data?.message || error.message,
      });
    },
  });

  // Reject farmer mutation
  const rejectFarmerMutation = useMutation({
    mutationFn: (userId: number) => rejectFarmer({ userId, userType }),
    onMutate: () => {
      toast.loading('Rejecting Account...', {
        description: 'Please wait...',
        duration: 90000,
      });
    },
    onSuccess: () => {
      toast.dismiss();
      toast.success('Success', {
        description: 'Account rejected successfully',
      });
      // Invalidate and refetch the farmers request query
      queryClient.invalidateQueries({ queryKey: ['farmersRequest'] });
    },
    onError: (error: any) => {
      toast.dismiss();
      toast.error('Oops! Something went wrong', {
        description: error?.response?.data?.message || error.message,
      });
    },
  });

  return {
    farmersRequestQuery,
    paginationState,
    approveFarmerMutation,
    rejectFarmerMutation,
  };
};

export type { IFarmerRequest, IFarmersRequestResponse };
