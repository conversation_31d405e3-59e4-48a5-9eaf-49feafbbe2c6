'use client';

import { format } from 'date-fns';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { ISoaData } from '@/lib/store/soa-store.types';
import { cn, toDecimal } from '@/lib/utils';

interface IPrintSoaProps {
  contentRef: any;
  orderInfo: ISoaData;
}

export default function PrintSoa({ contentRef, orderInfo }: IPrintSoaProps) {
  const gStateP = useGlobalStatePersist();

  return (
    <div
      ref={contentRef}
      className="hidden w-full font-dmSans print:block"
      style={{ fontFamily: 'DM Sans, sans-serif' }}
    >
      <div className="content-wrapper">
        {/* Header */}
        <div className="header mb-6">
          <div className="flex items-center justify-between">
            <div>
              <img className="h-14" src="/kita-logo.png" alt="" />
            </div>

            <div className="text-right text-xs italic">
              <div>{`Date & Time Generated: ${format(new Date(), 'MMM dd, yyyy - hh:mm a')}`}</div>
              <div className="mt-0.5">{`Order ID No. ${orderInfo.formatted.referenceNumber}`}</div>
            </div>
          </div>
        </div>

        <h1 className="mb-6 text-center text-2xl font-bold text-kitaph-blue">STATEMENT OF ACCOUNT</h1>

        {/* Farmer Info */}
        <div className="mb-6 flex text-sm">
          <div className="grid grid-cols-3 gap-6">
            <div>
              <div>Farmer Name</div>
              <div>Farmer Address</div>
              <div>No. of Hectares</div>
              <div>Main Crop To Be Planted</div>
              <div>Crop Type</div>
              <div>Sub Crop Planted</div>
              <div>Total E-wallet Amount</div>
            </div>
            <div className="col-span-2 font-bold">
              <div>
                {orderInfo.formatted.farmerName ? orderInfo.formatted.farmerName : <div className="invisible">-</div>}
              </div>
              <div>
                {orderInfo.formatted.farmAddress ? orderInfo.formatted.farmAddress : <div className="invisible">-</div>}
              </div>
              <div>
                {orderInfo.formatted.farmArea ? orderInfo.formatted.farmArea : <div className="invisible">-</div>}
              </div>
              <div>
                {orderInfo.formatted.mainCropPlanted ? (
                  orderInfo.formatted.mainCropPlanted
                ) : (
                  <div className="invisible">-</div>
                )}
              </div>
              <div>
                {orderInfo.formatted.cropType ? orderInfo.formatted.cropType : <div className="invisible">-</div>}
              </div>
              <div>
                {orderInfo.formatted.subCropPlanted ? (
                  orderInfo.formatted.subCropPlanted
                ) : (
                  <div className="invisible">-</div>
                )}
              </div>
              <div>Php {toDecimal(orderInfo.formatted.walletBalanceBefore || 0)}</div>
            </div>
          </div>
        </div>

        {/* Tables */}
        <div className="tables-container space-y-4">
          {/* Crops */}
          {orderInfo.formatted.crops.length > 0 && (
            <Table>
              <TableHeader className="dark border-none bg-kitaph-blue">
                <TableRow>
                  <TableHead className="h-8 w-full text-sm font-bold text-white">Crop</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {orderInfo.formatted.crops.map((crop, index) => {
                  return (
                    <TableRow key={index} className="border-none">
                      <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                        {crop.product}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {crop.quantity}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.price || 0)}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.total || 0)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}

          {/* Crop Protection Requirements */}
          {orderInfo.formatted.cropProtection.length > 0 && (
            <Table>
              <TableHeader className="dark border-none bg-kitaph-blue">
                <TableRow className="">
                  <TableHead className="h-8 w-full text-sm font-bold text-white">
                    Crop Protection Requirements
                  </TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {orderInfo.formatted.cropProtection.map((crop, index) => {
                  return (
                    <TableRow key={index} className="border-none">
                      <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                        {crop.product}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {crop.quantity}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.price || 0)}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.total || 0)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}

          {/* Fertilizer Requirements */}
          {orderInfo.formatted.fertilizer.length > 0 && (
            <Table>
              <TableHeader className="dark border-none bg-kitaph-blue">
                <TableRow className="">
                  <TableHead className="h-8 w-full text-sm font-bold text-white">Fertilizer Requirements</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {orderInfo.formatted.fertilizer.map((crop, index) => {
                  return (
                    <TableRow key={index} className="border-none">
                      <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                        {crop.product}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {crop.quantity}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.price || 0)}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.total || 0)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}

          {/* Seeds Requirements */}
          {orderInfo.formatted.seeds.length > 0 && (
            <Table>
              <TableHeader className="dark border-none bg-kitaph-blue">
                <TableRow className="">
                  <TableHead className="h-8 w-full text-sm font-bold text-white">Seeds Requirements</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {orderInfo.formatted.seeds.map((crop, index) => {
                  return (
                    <TableRow key={index} className="border-none">
                      <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                        {crop.product}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {crop.quantity}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.price || 0)}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.total || 0)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}

          {/* Miscellaneous */}
          {orderInfo.formatted.miscellaneous.length > 0 && (
            <Table>
              <TableHeader className="dark border-none bg-kitaph-blue">
                <TableRow className="">
                  <TableHead className="h-8 w-full text-sm font-bold text-white">Miscellaneous</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Quantity</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Price</TableHead>
                  <TableHead className="h-8 pl-12 text-right text-sm font-bold text-white">Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="">
                {orderInfo.formatted.miscellaneous.map((crop, index) => {
                  return (
                    <TableRow key={index} className="border-none">
                      <TableCell className={cn('pb-0 pt-1 font-medium', index === 0 && 'pt-3')}>
                        {crop.product}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {crop.quantity}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.price || 0)}
                      </TableCell>
                      <TableCell className={cn('pb-0 pt-1 text-right', index === 0 && 'pt-3')}>
                        {toDecimal(crop.total || 0)}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          )}
        </div>

        {/* Summary */}
        <div className="summary-section mt-8 flex justify-end pr-4 text-sm">
          <div className="grid grid-cols-2 gap-4 text-right">
            <div>
              <div className="mb-4 font-bold">Total Ordered Amount</div>
              <div>E-wallet Payment</div>
              <div>Cash Payment</div>
              <div>E-wallet Balance</div>
            </div>
            <div className="text-[17px] font-bold">
              <div className="mb-4">Php {toDecimal(orderInfo.formatted.totalPrice || 0)}</div>
              <div>
                Php{' '}
                {toDecimal(
                  orderInfo.raw.payment_method === 2
                    ? orderInfo.formatted.totalPrice // E-wallet only
                    : orderInfo.raw.payment_method === 3
                      ? orderInfo.formatted?.walletAllocation || 0 // Multiple
                      : 0, // Cash only
                )}
              </div>
              <div>
                Php{' '}
                {toDecimal(
                  orderInfo.raw.payment_method === 1
                    ? orderInfo.formatted.totalPrice // Cash only
                    : orderInfo.raw.payment_method === 3
                      ? orderInfo.formatted?.cashAllocation || 0 // Multiple
                      : 0, // E-wallet only
                )}
              </div>
              <div>Php {toDecimal(orderInfo.formatted.walletBalanceAfter || 0)}</div>
            </div>
          </div>
        </div>

        {/* Signature Section */}
        <div className="signature-section mb-8 mt-12">
          <div className="flex w-full items-stretch">
            <div className="w-1/4 border border-r-0 border-gray-500 p-2">
              <div>Created By</div>
              <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center">
                {gStateP.user.value && gStateP.user.user.admin.value
                  ? `${gStateP.user.user.admin.first_name.value} ${gStateP.user.user.admin.last_name.value}`
                  : ``}
              </div>
            </div>

            <div className="w-1/4 border border-r-0 border-gray-500 p-2">
              <div>Checked By</div>
              <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center"></div>
            </div>

            <div className="w-1/4 border border-r-0 border-gray-500 p-2">
              <div>Approved By</div>
              <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center"></div>
            </div>

            <div className="w-1/4 border border-gray-500 p-2">
              <div>Received By</div>
              <div className="mt-10 h-6 min-h-[3em] w-full border-t border-gray-400 text-center">
                {orderInfo.formatted.farmerName}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @font-face {
          font-family: 'DM Sans';
          src: url('/fonts/dm-sans/DMSans-Regular.ttf') format('truetype');
          font-weight: normal;
          font-style: normal;
          font-display: swap;
        }

        @font-face {
          font-family: 'DM Sans';
          src: url('/fonts/dm-sans/DMSans-Bold.ttf') format('truetype');
          font-weight: bold;
          font-style: normal;
          font-display: swap;
        }

        @font-face {
          font-family: 'DM Sans';
          src: url('/fonts/dm-sans/DMSans-Medium.ttf') format('truetype');
          font-weight: 500;
          font-style: normal;
          font-display: swap;
        }

        @font-face {
          font-family: 'DM Sans';
          src: url('/fonts/dm-sans/DMSans-Italic.ttf') format('truetype');
          font-weight: normal;
          font-style: italic;
          font-display: swap;
        }

        @media print {
          html,
          body {
            height: auto !important;
            overflow: visible !important;
            -webkit-print-color-adjust: exact;
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'DM Sans', sans-serif !important;
          }

          * {
            font-family: 'DM Sans', sans-serif !important;
          }

          @page {
            size: A4;
            margin: 12mm 14mm 20mm 14mm;
          }

          * {
            box-sizing: border-box;
          }

          .header {
            margin-bottom: 1.5rem;
          }

          /* Prevent page breaks inside tables */
          table {
            page-break-inside: avoid;
            break-inside: avoid;
          }

          /* Allow page breaks between table sections */
          .space-y-4 > * {
            page-break-inside: avoid;
            break-inside: avoid;
            margin-bottom: 1rem;
          }

          /* Ensure signature section stays together */
          .signature-section {
            page-break-inside: avoid;
            break-inside: avoid;
            margin-top: 2rem;
          }

          /* Force page break before signature if needed */
          .signature-section {
            page-break-before: auto;
          }

          /* Prevent orphaned content */
          .summary-section {
            page-break-inside: avoid;
            break-inside: avoid;
          }

          /* Ensure minimum space for signature section */
          .content-wrapper {
            min-height: calc(100vh - 8rem);
            display: flex;
            flex-direction: column;
          }

          .tables-container {
            flex: 1;
          }
        }
      `}</style>
    </div>
  );
}
