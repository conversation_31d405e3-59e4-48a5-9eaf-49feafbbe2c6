export default function Page3({ data }) {
  return (
    <div className="relative mx-auto flex h-[11in] w-[8.5in] flex-col border bg-contain bg-top bg-no-repeat px-8 py-12 text-[14px] capitalize leading-relaxed text-gray-900 print:border-none">
      <div className="mx-auto max-w-[600px]">
        <ol start={9} className="ml-5 list-decimal space-y-3">
          {/* 9 */}
          <li>
            <span className="font-bold">Additional Leasehold Rights</span>
            <ol className="ml-5 mt-1 list-[lower-alpha] space-y-1">
              <li>
                <span className="font-semibold italic">Land Transfer or Sale:</span> Should the LESSOR opt to sell or
                transfer the ownership of the landholding; the rights of the LESSEE shall not be invalidated and remain
                valid under this Agreement unless a new agreement is settled between the LESSEE and the new owner.
              </li>
              <li>
                <span className="font-semibold italic">Inheritance Rights:</span> If the LESSEE passes away or becomes
                permanently incapable to farm, his/her heirs and/or immediate family members can continue farming under
                the same lease agreement, upon written notice to the LESSOR.
              </li>
            </ol>
          </li>
        </ol>

        {/* Signature Section */}
        <div className="mt-12">
          <p>
            IN WITNESS WHEREOF, the parties hereto signed this Agreement this{' '}
            <span className="inline-block w-16 border-b border-black"></span> day of{' '}
            <span className="inline-block w-24 border-b border-black"></span>, 20
            <span className="inline-block w-12 border-b border-black"></span>, in{' '}
            <span className="inline-block w-48 border-b border-black"></span>.
          </p>

          <div className="mt-8 grid grid-cols-2 gap-12 text-center">
            <div className="flex items-center">
              <span>By:</span>
              <div>
                <b>LESSOR</b>
                <div className="h-16"></div>
                <div className="mx-auto w-48 border-b border-black"></div>
              </div>
            </div>
            <div className="flex items-center">
              <span>By:</span>
              <div>
                <b>LESSEE</b>
                <div className="flex h-16 items-end justify-center font-bold">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>
                <div className="mx-auto w-48 border-b border-black"></div>
              </div>
            </div>
          </div>

          <div className="mt-8 grid grid-cols-2 gap-12 text-center">
            <div className="flex items-center">
              <span>By:</span>
              <div>
                <b>WITNESS</b>
                <div className="h-16"></div>
                <div className="mx-auto w-48 border-b border-black"></div>
              </div>
            </div>
            <div className="flex items-center">
              <span>By:</span>
              <div>
                <b>WITNESS</b>
                <div className="h-16"></div>
                <div className="mx-auto w-48 border-b border-black"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
