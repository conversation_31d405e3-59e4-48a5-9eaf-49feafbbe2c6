import { BookText, BookUserIcon, HomeIcon, Store, UserRoundCog } from 'lucide-react';
import { FaHandHoldingUsd } from 'react-icons/fa';
import { MdCurrencyExchange, MdOutlineCreditScore } from 'react-icons/md';

export const MENU = [
  {
    id: 0,
    name: 'Dashboard',
    icon: <HomeIcon className="size-5" />,
    href: '/admin',
  },
  {
    id: 1,
    name: 'Accounts',
    icon: <BookUserIcon className="size-5" />,
    href: '/admin/accounts/',
  },
  {
    id: 2,
    name: 'Product Management',
    icon: <BookText className="size-5" />,
    href: '/admin/product-management/',
  },
  {
    id: 3,
    name: 'Trading Post',
    icon: <FaHandHoldingUsd className="size-5" />,
    href: '/admin/trading-post/',
  },
  {
    id: 4,
    name: 'Trading App',
    icon: <MdCurrencyExchange className="size-5" />,
    href: '/admin/trading-app/',
  },
  {
    id: 5,
    name: 'Marketplace',
    icon: <Store className="size-5" />,
    href: '/admin/marketplace/',
  },
  {
    id: 6,
    name: 'User Management',
    icon: <UserRoundCog className="size-5" />,
    href: '/admin/user-management/',
  },
  {
    id: 7,
    name: 'Credit Score Management',
    icon: <MdOutlineCreditScore className="size-5" />,
    href: '/admin/credit-score-management/',
  },
];

export enum TAB_MARKETPLACE {
  ORDERS = 'orders',
  PRODUCTS = 'products',
  EWALLET = 'e-wallet',
  PAYMENTS = 'payments',
  REPORTS = 'reports',
}

export enum TAB_TRADING_APP {
  QA = 'qa',
  BUYER = 'buyer',
  SELLER = 'seller',
}
