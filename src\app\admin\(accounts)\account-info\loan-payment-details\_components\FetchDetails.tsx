'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import useFinance from '@/lib/hooks/useFinance';

export default function FetchDetails() {
  const detailsId = useSearchParams().get('id');
  const { getLoanPaymentDetails } = useFinance();
  const router = useRouter();

  useEffect(() => {
    if (detailsId) {
      getLoanPaymentDetails(detailsId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.back();
    }
  }, [detailsId]);

  return null;
}
