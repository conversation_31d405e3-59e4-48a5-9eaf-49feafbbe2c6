import { zodResolver } from '@hookform/resolvers/zod';
import { State } from '@hookstate/core';
import { LocalStored } from '@hookstate/localstored';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';

import { useDisableNumberInputScroll } from '@/lib/hooks/utils/useDisableNumberInputScroll';

import { personalInformationSchema, TPersonalInformationSchema } from '../../schemas';
import { IFROState, IPersonalInformation } from '../../types';
import IdentificationDocs from './IdentificationDocs';
import PersonalDetails from './PersonalDetails';
import PresentAddress from './PresentAddress';

interface IPersonalInfoProps {
  gStateFRO: State<IFROState, LocalStored>;
  onPrevious: () => void;
  onNext: () => void;
  onClear: () => void;
}

const PersonalInfo = ({ gStateFRO, onPrevious, onNext, onClear }: IPersonalInfoProps) => {
  useDisableNumberInputScroll();
  const data = gStateFRO?.form.step2?.value;
  const {
    register,
    control,
    formState: { errors },
    setValue,
    watch,
    handleSubmit,
    getValues: values,
  } = useForm<TPersonalInformationSchema>({
    resolver: zodResolver(personalInformationSchema),
    defaultValues: {
      // Personal Details
      firstName: data?.firstName || '',
      middleName: data?.middleName || '',
      lastName: data?.lastName || '',
      birthDate: data?.birthDate || '',
      gender: data?.gender || '',
      nationality: 'Filipino',
      mobileNumber: data?.mobileNumber || '',
      placeOfBirth: data?.placeOfBirth || '',
      email: data?.email || '',
      telephoneNumber: data?.telephoneNumber || '',
      facebookName: data?.facebookName || '',
      civilStatus: data?.civilStatus || '',
      spouseName: data?.civilStatus === 'MARRIED' ? data?.spouseName : '',
      spouseMobileNumber: data?.civilStatus === 'MARRIED' ? data?.spouseMobileNumber : '',

      // Present Address
      addressHouseNumber: data?.addressHouseNumber || '',
      addressStreet: data?.addressStreet || '',
      addressRegion: data?.addressRegion || '',
      addressProvince: data?.addressProvince || '',
      addressCity: data?.addressCity || '',
      addressBarangay: data?.addressBarangay || '',
      addressZipCode: data?.addressZipCode || '',
      addressLengthOfStay: data?.addressLengthOfStay || '',
      residenceOwnership: data?.residenceOwnership || '',

      // Identification Docs
      governmentIdentification: data?.governmentIdentification
        ? Array.from(data.governmentIdentification)
        : [
            {
              governmentIdType: '',
              governmentIdNumber: '',
              upload: null,
            },
          ],
    },
  });

  const handleNext = handleSubmit(() => onNext());

  const handlePrevious = () => onPrevious();

  useEffect(() => {
    const subscription = watch((value, { name, type }) => {
      gStateFRO.form.step2.set(value as IPersonalInformation);
    });

    return () => subscription.unsubscribe();
  }, [watch()]);

  console.log('Step2:', { errors, values: values() });

  return (
    <form onSubmit={handleNext}>
      <PersonalDetails
        register={register}
        control={control}
        errors={errors}
        setValue={setValue}
        watch={watch}
        values={values}
      />
      <PresentAddress register={register} control={control} errors={errors} values={values} />
      <IdentificationDocs register={register} control={control} errors={errors} watch={watch} values={values} />

      <div className="mt-16 flex justify-between">
        <Button type="button" variant="outline" onClick={onClear}>
          Clear All Forms
        </Button>
        <div className="flex justify-center gap-4">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit">Next</Button>
        </div>
      </div>
    </form>
  );
};

export default PersonalInfo;
