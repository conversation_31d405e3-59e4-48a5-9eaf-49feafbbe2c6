import { useHookstate } from '@hookstate/core';
import { format, isValid, parse } from 'date-fns';
import { EyeIcon, UploadCloud } from 'lucide-react';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { useGlobalState } from '@/lib/store';
import { IUserBulkUpload } from '@/lib/types/user';
import { handleDownload } from '@/lib/utils';

import { useBulkImportUsers } from '../hooks/useBulkImportUsers';
import UserCsv from './_components/UsersCsv';

export const bulkImportColumns = [
  {
    id: 'id',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Uploaded" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.created_at), 'dd MMM yyyy | hh:mm a')}</div>;
    },
    accessorFn: (row) => format(new Date(row.created_at), 'dd MMM yyyy | hh:mm a'),
  },
  {
    id: 'total_users',
    header: ({ column }) => <DataTableColumnHeader column={column} title="No. of Users" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.total_users}</div>;
    },
  },
  {
    id: 'processedBy',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Uploaded By" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${data.processedBy.admin.first_name} ${data.processedBy.admin.last_name}`}</div>
      );
    },
    accessorFn: (row) => `${row.processedBy.admin.first_name} ${row.processedBy.admin.last_name}`,
    enableSorting: false, // Disable sorting due to backend SQL ambiguity with date filters
  },
  {
    id: 'actions',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Attachments" className="text-center" />,
    cell: ({ row }) => <BulkImportAction row={row} />,
  },
];

const BulkImportAction = ({ row }) => {
  const data = row.original;

  return (
    <div className="flex justify-center gap-2">
      <Button
        type="button"
        className="flex h-8 items-center gap-2 px-2 lg:px-3"
        variant="outline"
        size="sm"
        onClick={() => handleDownload(data.attachment, 'imported-farmers.csv')}
      >
        <EyeIcon className="size-4" /> Download File
      </Button>
    </div>
  );
};

export const BulkImportActionHeader = () => {
  const gState = useGlobalState();
  const importDialog = useHookstate(false);

  const { bulkUploadMutation } = useBulkImportUsers();

  return (
    <div className="flex items-center justify-end gap-2">
      <Button
        className="h-8 px-2 lg:px-3"
        size="sm"
        onClick={() => {
          importDialog.set(true);
        }}
      >
        Import Bulk Users
        <UploadCloud className="ml-2 size-4 transition duration-300 ease-in-out" />
      </Button>

      <Dialog open={importDialog.value} onOpenChange={importDialog.set}>
        <DialogContent className="min-h-max p-0 sm:max-w-6xl">
          <DialogHeader className="px-6 pt-6">
            <DialogTitle className="text-primary">Import Users</DialogTitle>
            <DialogDescription>{`Upload CSV file to add users`}</DialogDescription>
          </DialogHeader>

          <UserCsv />

          <div className="flex justify-between gap-2 p-6">
            <DialogClose asChild>
              <Button className="px-12" variant="outline" type="button">
                Cancel
              </Button>
            </DialogClose>

            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button className="px-12" type="button" disabled={gState.admin.usersUpload.length === 0}>
                  Add
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone.
                    <br /> The ff. will be automatically excluded from this upload process.
                    <br /> - users without a <b>birthdate</b>
                    <br /> - <b>existing</b> users
                    <br />
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel type="button">Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    type="button"
                    onClick={async () => {
                      const rawUsers = gState.admin.usersUpload.get({ noproxy: true });

                      const users = {
                        users: rawUsers,
                        attachment: gState.admin.usersUploadFile.get({ noproxy: true }),
                      } as IUserBulkUpload;

                      bulkUploadMutation.mutate(users, {
                        onSuccess: () => {
                          importDialog.set(false);
                        },
                      });
                    }}
                  >
                    Continue
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
