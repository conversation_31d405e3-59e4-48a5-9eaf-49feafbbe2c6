'use client';

import { useRef } from 'react';
import { useReactToPrint } from 'react-to-print';

import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

import { safeJsonParse } from '@/lib/hooks/usePdfForm';

export default function TripartiteAgreementForm({
  isDialogOpen,
  setIsDialogOpen,
  data: farmerData,
}: {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: any;
}) {
  const contentRef = useRef<HTMLDivElement>(null);

  const reactToPrintFn = useReactToPrint({
    contentRef,
    documentTitle: 'Tripartite Agreement Form',
  });

  const data = farmerData?.farmer;

  const now = new Date();

  const day = now.getDate(); // returns the day (1–31)
  const month = now.toLocaleString('default', { month: 'long' });

  return (
    <div className="">
      <Dialog
        open={isDialogOpen}
        onOpenChange={(open) => {
          setIsDialogOpen(open);
        }}
      >
        <DialogContent className="max-w-[95vw] overflow-auto md:max-w-[85vw] lg:max-w-[75vw] xl:max-w-4xl">
          <DialogHeader>
            <DialogTitle>Tripartite Agreement Form</DialogTitle>
          </DialogHeader>
          <div className="mx-auto max-h-[50vh] max-w-4xl flex-1 flex-row justify-center overflow-auto sm:max-h-[60vh] lg:max-h-[80vh]">
            <div ref={contentRef}>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-1.jpg)] bg-contain bg-top bg-no-repeat capitalize">
                {/* Day and month */}
                <div className="absolute right-[12.3rem] top-[10.5rem]">{day}</div>
                <div className="absolute right-[6.5rem] top-[10.5rem]">{month}</div>

                {/* Farmer name */}
                <div className="absolute left-[6.9rem] top-[19.13rem] w-[190px] border-b-2 border-black bg-white text-center text-[15px] uppercase leading-none">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>

                {/* Barangay */}
                <div className="absolute right-48 top-[19.13rem] w-[100px] border-b-2 border-black bg-white text-center text-[15px] uppercase leading-none">
                  {safeJsonParse(data?.address_barangay, 'brgy_name')}
                </div>

                {/* City/Town */}
                <div className="absolute left-[8.5rem] top-[20.2rem] w-[215px] border-b-2 border-black bg-white text-center text-[15px] uppercase leading-none">
                  {safeJsonParse(data?.address_city, 'city_name')}
                </div>

                {/* Province */}
                <div className="absolute left-[23.4rem] top-[20.2rem] w-[102px] border-b-2 border-black bg-white text-center text-[15px] uppercase leading-none">
                  {safeJsonParse(data?.address_province, 'province_name')}
                </div>
                <div className="absolute left-[18.5rem] top-[28.5rem] h-4 w-[200px] border-b-[1.5px] border-black bg-white"></div>
              </div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-2.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-3.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-4.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-5.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-6.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-7.jpg)] bg-contain bg-top bg-no-repeat capitalize">
                {/* Farmer name */}
                <div className="absolute left-[18.5rem] top-[19rem] w-[200px] uppercase">{`${data?.first_name} ${data?.middle_name} ${data?.last_name}`}</div>
              </div>
              <div className="relative flex h-[14in] w-[8.5in] flex-col bg-[url(/assets/forms/tripartite-agreement-form/tripartite-agreement-form-8.jpg)] bg-contain bg-top bg-no-repeat capitalize"></div>
            </div>
          </div>

          <DialogFooter className="mt-4">
            <Button type="submit" onClick={() => reactToPrintFn()}>
              Download
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
