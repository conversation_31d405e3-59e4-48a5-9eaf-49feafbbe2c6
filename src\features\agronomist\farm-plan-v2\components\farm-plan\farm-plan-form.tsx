'use client';

import { format } from 'date-fns';

import { IFarmPlanResponse } from '../../types/farm-plan.types';
import { buildHomeAddress, getFarmAddress, getFarmArea } from '../../utils/address-utils';
import Page1 from './page1';
import Page2 from './page2';

interface IPrintFarmPlanProps {
  contentRef: any;
  farmPlan: IFarmPlanResponse;
}

// interface GroupedItems {
//   [expectedDate: string]: {
//     [category: string]: IFarmPlanResponse['farmPlanItems'][0]['farmPlanSubItems'];
//   };
// }

export default function FarmPlanForm({ contentRef, farmPlan }) {
  // Group farm plan sub items by expected date and category
  // const groupedItems: GroupedItems = {};

  // farmPlan.farmPlanItems.forEach((item) => {
  //   if (Array.isArray(item.farmPlanSubItems)) {
  //     item.farmPlanSubItems.forEach((subItem) => {
  //       const expectedDate = subItem.expected_date;
  //       const category = item.name;

  //       if (!groupedItems[expectedDate]) {
  //         groupedItems[expectedDate] = {};
  //       }
  //       if (!groupedItems[expectedDate][category]) {
  //         groupedItems[expectedDate][category] = [];
  //       }

  //       groupedItems[expectedDate][category].push(subItem);
  //     });
  //   }
  // });

  // Calculate subtotals for each group
  // const calculateSubtotal = (items: IFarmPlanResponse['farmPlanItems'][0]['farmPlanSubItems']) => {
  //   return items.reduce((sum, item) => sum + item.total_amount, 0);
  // };

  // Calculate grand total
  // const grandTotal = farmPlan.total_amount || 0;

  // const farmer = farmPlan.user.farmer;

  console.log('Farm Plan Data:', farmPlan);

  return (
    <div
      ref={contentRef}
      className="hidden w-full font-dmSans print:block"
      style={{ fontFamily: 'DM Sans, sans-serif' }}
    >
      <Page1 data={farmPlan} />
      <Page2 data={farmPlan} />

      <style jsx>{`
        @media print {
          html,
          body {
            height: auto !important;
            overflow: visible !important;
            -webkit-print-color-adjust: exact;
            margin: 0 !important;
            padding: 0 !important;
            font-family: 'DM Sans', sans-serif !important;
          }

          * {
            font-family: 'DM Sans', sans-serif !important;
          }

          @page {
            size: LEGAL;
          }

          .signature-section {
            page-break-inside: avoid;
            break-inside: avoid;
            margin-top: 2rem;
          }

          table {
            page-break-inside: avoid;
            break-inside: avoid;
          }
        }
      `}</style>
    </div>
  );
}
