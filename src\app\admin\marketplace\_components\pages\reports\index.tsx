'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { marketplaceReportsState, useMarketplaceReportsStore } from '@/lib/store/marketplace-reports-store';

import { ReportsTable } from './reports-table';
import { ReportsColumns } from './reports-table/columns';

export default function ReportsPage() {
  const state = useHookstate(marketplaceReportsState.data);
  const query = useHookstate(marketplaceReportsState.query);
  const { getReports } = useMarketplaceReportsStore();

  useEffect(() => {
    getReports();
  }, [query.page, query.pageSize, query.startDate, query.endDate, query.regions]);

  return (
    <div className="p-4 md:p-8">
      <ReportsTable
        data={state.data.get({ noproxy: true })}
        columns={ReportsColumns}
        meta={state.meta.get({ noproxy: true })}
      />
    </div>
  );
}
