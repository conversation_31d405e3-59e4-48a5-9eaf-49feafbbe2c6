'use client';

import { cn } from '@/lib/utils';

import { MENU } from './constants';

export default function MenuMapper({ gStateP, mobileMenu, router }) {
  return (
    <section className="h-[calc(100vh-76.8px)] space-y-3 py-[32px] pr-4 lg:w-[290px]">
      {MENU.map((menu) => {
        const isActive = menu.id === gStateP.admin.activeMenu.value;

        return (
          <div key={menu.id} className="flex gap-4">
            <div className={cn('w-1 bg-blue-900 rounded-r-3xl', isActive ? 'visible' : 'invisible')} />

            <button
              className={cn(
                'w-full flex text-left text-sm font-bold items-center gap-3 px-4 py-2 rounded-md transition duration-300 ease-in-out',
                isActive ? 'text-primary bg-blue-400/20' : 'text-slate-400', // active
                'hover:text-primary hover:bg-blue-400/20',
              )}
              onClick={() => {
                gStateP.admin.activeMenu.set(menu.id);
                mobileMenu.set(false);
                router.push(menu.href);
              }}
            >
              {menu.icon}
              <span className="">{menu.name}</span>
            </button>
          </div>
        );
      })}
    </section>
  );
}
