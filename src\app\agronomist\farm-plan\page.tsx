'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import FarmPlan from '@/features/agronomist/farm-plan-v2';
import { useGlobalStatePersist } from '@/lib/store/persist';

export default function FarmPlanPage() {
  const gStateP = useGlobalStatePersist();
  const activeMenu = useHookstate(gStateP.agronomist.activeMenu);

  // Set active menu to Farm Plan & Calculator (id: 1)
  useEffect(() => {
    activeMenu.set(1);
  }, []);

  return (
    <div className="p-6">
      <FarmPlan />
    </div>
  );
}
