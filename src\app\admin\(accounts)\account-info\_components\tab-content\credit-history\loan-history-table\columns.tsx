'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import { Badge } from '@/components/ui/badge';

import { PaymentStatusLabels } from '@/app/admin/marketplace/_components/Enums';
import { useGlobalState } from '@/lib/store';
import { cn } from '@/lib/utils';

export const columnsLoanHistory = [
  {
    id: 'payment_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Final Payment Date" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="min-w-max">
          {data.loanPeriodTracker?.topupRequest?.paid_at
            ? format(new Date(data.loanPeriodTracker?.topupRequest?.paid_at), 'MMM dd, yyyy')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return `${format(new Date(data.loanPeriodTracker?.topupRequest?.paid_at), 'MMM dd, yyyy')}`;
    },
  },
  {
    id: 'payment_processed',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Date Processed" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data.loanPeriodTracker?.topupRequest?.updated_at
            ? format(new Date(data.loanPeriodTracker?.topupRequest?.updated_at), 'MMM dd, yyyy | hh:mm aa')
            : '-'}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return `${data?.loanPeriodTracker && format(new Date(data.loanPeriodTracker?.topupRequest?.updated_at), 'MMM dd, yyyy | hh:mm aa')}`;
    },
  },
  {
    id: 'start_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Start Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data?.loanPeriodTracker && format(new Date(data?.loanPeriodTracker?.before_loan_start_at), 'MMM dd, yyyy')}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return `${data?.loanPeriodTracker && format(new Date(data?.loanPeriodTracker?.before_loan_start_at), 'MMM dd, yyyy')}`;
    },
  },
  {
    id: 'end_date',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan End Date" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">
          {data?.loanPeriodTracker && format(new Date(data.loanPeriodTracker?.after_loan_end_at), 'MMM dd, yyyy')}
        </div>
      );
    },
    accessorFn: (row) => {
      const data = row;
      return `${format(new Date(data.loanPeriodTracker?.after_loan_end_at), 'MMM dd, yyyy')}`;
    },
  },
  {
    id: 'loan_terms',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Term" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{`${data.loanPeriodTracker?.topupRequest?.loan_term ?? 0} days`}</div>;
    },
    accessorFn: (row) => {
      return `${row.loanPeriodTracker?.topupRequest?.loan_term}`;
    },
  },
  {
    id: 'group',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Credit Score Group" />,
    cell: ({ row }) => {
      const data = row.original;

      return <div className="min-w-max">{<CreditScoreGroup id={data?.credit_score_group_id} />}</div>;
    },
    accessorFn: (row) => {
      return <CreditScoreGroup id={row?.credit_score_group_id} />;
    },
  },
  {
    id: 'before_score',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Before Loan Score" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data?.before_stage_credit_score || 0}%</div>;
    },
    accessorFn: (row) => {
      return `${row?.before_stage_credit_score}%`;
    },
  },
  {
    id: 'during_score',
    header: ({ column }) => <DataTableColumnHeader column={column} title="During Loan Score" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data?.during_stage_credit_score || 0}%</div>;
    },
    accessorFn: (row) => {
      return `${row?.during_stage_credit_score}%`;
    },
  },
  {
    id: 'after_score',
    header: ({ column }) => <DataTableColumnHeader column={column} title="After Loan Score" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data?.after_stage_credit_score || 0}%</div>;
    },
    accessorFn: (row) => {
      return `${row?.after_stage_credit_score}%`;
    },
  },
  {
    id: 'payment_status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Payment Status" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <>
          {data.loanPeriodTracker?.topupRequest && (
            <Badge
              className={cn(
                PaymentStatusLabels[data?.loanPeriodTracker?.topupRequest?.payment_status].color,
                'min-w-max',
              )}
            >
              {PaymentStatusLabels[data?.loanPeriodTracker?.topupRequest?.payment_status].label}
            </Badge>
          )}
        </>
      );
    },
    accessorFn: (row) => {
      return `${PaymentStatusLabels[row.loanPeriodTracker?.topupRequest?.payment_status].label}`;
    },
  },
  {
    id: 'loan_balance',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Loan Balance" />,
    cell: ({ row }) => {
      const data = row.original;
      return (
        <div className="min-w-max">{`${Number(data.loanPeriodTracker?.topupRequest?.total_loan_amount).toLocaleString(
          'en-US',
          {
            style: 'currency',
            currency: 'PHP',
          },
        )}`}</div>
      );
    },
    accessorFn: (row) => {
      return `${row?.loanPeriodTracker?.topupRequest?.total_loan_amount}`;
    },
  },
  // {
  //   id: 'repayment_behavior',
  //   header: ({ column }) => <DataTableColumnHeader column={column} title="Repayment Behavior" />,
  //   cell: ({ row }) => {
  //     const data = row.original;
  //     return <div className="min-w-max">-</div>;
  //   },
  //   accessorFn: (row) => {
  //     const data = row;
  //     return `-`;
  //   },
  // },
];

const CreditScoreGroup = ({ id }) => {
  const gState = useGlobalState();
  const groups = useHookstate(gState.admin.creditScoreMgt.groups.data);
  const group = groups.find((group) => group.get({ noproxy: true }).id === id);

  return group ? group.get({ noproxy: true }).name : '-';
};
