'use client';

import { format } from 'date-fns';
import { useMemo } from 'react';

import { FormDimensions } from '@/lib/constants/enums';

interface FarmPlanSubItem {
  id: number;
  farm_plan_item_id: number;
  farm_plan_id: number;
  expected_date: string;
  item_name: string;
  unit: string;
  quantity: number;
  unit_cost: number;
  total_amount: number;
  notes: string;
  marketplace_product_id: number;
  created_at: string;
  updated_at: string;
}

interface FarmPlanItem {
  id: number;
  farm_plan_id: number;
  name: string;
  slug: string;
  type: string;
  total_amount: number | null;
  created_at: string;
  updated_at: string;
  farmPlanSubItems: FarmPlanSubItem[];
}

interface GroupedItems {
  [expectedDate: string]: {
    [category: string]: FarmPlanSubItem[];
  };
}

interface FarmerData {
  farmer: {
    first_name: string;
    last_name: string;
    address?: string;
    farmerInfo?: {
      farm_address?: string;
      farm_area?: string;
    };
  };
  farmPlans: Array<{
    reference_number: string;
    total_amount: number;
    cropping_type: string;
    agronomist_name: string;
    agronomist_prc_number: string;
    agronomist_valid_until: string;
    head_agronomist_name: string;
    head_agronomist_prc_number: string;
    head_agronomist_valid_until: string;
    crop?: {
      name: string;
    };
    farmPlanItems: FarmPlanItem[];
  }>;
}

interface PrintFarmPlanProps {
  isDialogOpen: boolean;
  setIsDialogOpen: (open: boolean) => void;
  data: FarmerData;
}

const Page2 = ({ data: farmerData }) => {
  const farmPlan = farmerData;
  const address = farmerData?.farmer?.address ? JSON.parse(farmerData.farmer.address) : {};

  const costSummary = useMemo(() => {
    const items = farmPlan.farmPlanItems || [];
    const contingencyPercentage = farmPlan.contingency_for_fluctuation || 0;

    // Farm input item names (matching the logic from admin forms and edit form)
    const farmInputNames = [
      'Seed / Seedling Requirements (SE)',
      'Soil Fertilization - Basal (Top-Dress) (FE)',
      'Soil Fertilization - Additional (Side-Dress) (FE)',
      'Foliar Fertilization (Spray) (FE)',
      'Pesticide Application (Spray / Spread) (CP)',
      'Farm Materials, Consumables, etc.',
    ];

    // Cash requirements item names
    const cashRequirementsNames = ['Labor Requirements', 'Other Production Costs'];

    // Farmer's equity item names
    const farmersEquityNames = [
      'Non-Cash Costs',
      'KITA Subsidized Costs',
      'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)',
    ];

    // Calculate farm inputs total (before contingency)
    const farmInputsTotal = items
      .filter((item) => farmInputNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    // Calculate farm inputs with contingency (matching edit form logic)
    const farmInputsContingency = farmInputsTotal + (farmInputsTotal * contingencyPercentage) / 100;

    // Calculate cash requirements
    const cashRequirements = items
      .filter((item) => cashRequirementsNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    // Calculate farmer's equity
    const farmersEquity = items
      .filter((item) => farmersEquityNames.includes(item.name))
      .reduce((acc, item) => acc + (item.total_amount || 0), 0);

    const totalProductionCosts = farmInputsContingency + cashRequirements + farmersEquity;

    const lbpShare = totalProductionCosts - farmersEquity;

    const interestRate = farmPlan?.interest_rate;
    const numberOfMonths = farmPlan?.number_of_months_per_tenor;

    const estimatedComputedInterest =
      lbpShare > 0 && interestRate > 0 && numberOfMonths > 0
        ? lbpShare * (interestRate / 100) * (numberOfMonths / 12)
        : 0;

    const principalPlusInterest = lbpShare + estimatedComputedInterest;

    return {
      lbpShare,
      farmInputsContingency,
      estimatedComputedInterest,
      principalPlusInterest,
      cashRequirements,
      farmersEquity,
      totalProductionCosts,
      farmInputsPercentage: totalProductionCosts > 0 ? (farmInputsContingency / totalProductionCosts) * 100 : 0,
      cashRequirementsPercentage: totalProductionCosts > 0 ? (cashRequirements / totalProductionCosts) * 100 : 0,
      farmersEquityPercentage: totalProductionCosts > 0 ? (farmersEquity / totalProductionCosts) * 100 : 0,
    };
  }, [
    farmPlan?.farmPlanItems,
    farmPlan?.contingency_for_fluctuation,
    farmPlan?.interest_rate,
    farmPlan?.number_of_months_per_tenor,
  ]);

  const formatCurrency = (value: number) => {
    return value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  // Group farm plan sub items by expected date and category
  const groupedItems: GroupedItems = {};

  farmPlan?.farmPlanItems?.forEach((item: FarmPlanItem) => {
    if (Array.isArray(item.farmPlanSubItems)) {
      item.farmPlanSubItems.forEach((subItem: FarmPlanSubItem) => {
        const expectedDate = subItem.expected_date;
        const category = item.name;

        if (!groupedItems[expectedDate]) {
          groupedItems[expectedDate] = {};
        }
        if (!groupedItems[expectedDate][category]) {
          groupedItems[expectedDate][category] = [];
        }

        groupedItems[expectedDate][category].push(subItem);
      });
    }
  });

  // Calculate subtotals for each group
  const calculateSubtotal = (items: FarmPlanSubItem[]) => {
    return items.reduce((sum, item) => sum + item.total_amount, 0);
  };

  return (
    <div
      className={`relative flex ${FormDimensions.LEGAL} flex-col border p-10 font-sans capitalize print:border-none`}
    >
      <div className="w-full bg-gray-300 py-1 text-center font-semibold">
        CASH REQUIREMENTS (Labor, Overhead, Other Cash Costs, Etc.)
      </div>
      {Object.entries(groupedItems)
        .flatMap(([expectedDate, categories]) =>
          Object.entries(categories).map(([category, items]) => ({
            expectedDate,
            category,
            items,
          })),
        )
        .slice(-5)
        .map(({ expectedDate, category, items }) => {
          const subtotal = calculateSubtotal(items);

          return (
            <div key={`${expectedDate}-${category}`} className="mb-3">
              <div className="border bg-white py-1 text-center text-sm font-medium text-black">{category}</div>

              <table className="w-full border-collapse border border-gray-400 text-xs">
                <thead>
                  <tr className="bg-gray-300 text-black">
                    <th className="border border-gray-400 p-2 text-left">Expected Date</th>
                    <th className="border border-gray-400 p-2 text-left">Items</th>
                    <th className="border border-gray-400 p-2 text-center">Unit</th>
                    <th className="border border-gray-400 p-2 text-center">Quantity</th>
                    <th className="border border-gray-400 p-2 text-center">Unit Cost</th>
                    <th className="border border-gray-400 p-2 text-center">Total Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {items.map((item, index) => (
                    <tr key={item.id}>
                      <td className="border border-gray-400 p-2">
                        {index === 0 ? format(new Date(expectedDate), 'MM/dd/yyyy') : ''}
                      </td>
                      <td className="border border-gray-400 p-2">{item.item_name}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.unit}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.quantity}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.unit_cost.toFixed(2)}</td>
                      <td className="border border-gray-400 p-2 text-center">{item.total_amount.toFixed(2)}</td>
                    </tr>
                  ))}
                  <tr>
                    <td colSpan={5} className="border border-gray-400 p-2 text-right font-medium">
                      Sub Total
                    </td>
                    <td className="border border-gray-400 p-2 text-center font-medium">{subtotal.toFixed(2)}</td>
                  </tr>
                  {category === 'Other Production Costs' && (
                    <>
                      <tr>
                        <td colSpan={5} className="border border-gray-400 bg-gray-300 p-2 text-right font-medium">
                          TOTAL CASH REQUIREMENTS
                        </td>
                        <td className="border border-gray-400 p-2 text-center font-medium">
                          {formatCurrency(costSummary.cashRequirements)}
                        </td>
                      </tr>
                    </>
                  )}

                  {category === 'Non-KITA Subsidized Costs (Government Subsidy, Grants, Donations, etc.)' && (
                    <>
                      <tr>
                        <td colSpan={5} className="border border-gray-400 bg-gray-300 p-2 text-right font-medium">
                          TOTAL FARMER&apos;S EQUITY
                        </td>
                        <td className="border border-gray-400 p-2 text-center font-medium">
                          {formatCurrency(costSummary.farmersEquity)}
                        </td>
                      </tr>
                    </>
                  )}
                </tbody>
              </table>
            </div>
          );
        })}

      <div className="flex w-full space-x-2">
        <div className="flex flex-col pt-8">
          <div className="grid w-full grid-cols-2 space-x-2 text-xs">
            <div className="flex flex-col">
              <p className="mb-6 py-2">Prepared by:</p>

              <span className="mx-4 inline-block border-b border-black px-8 text-center ">
                {farmPlan?.head_agronomist_name}
              </span>
              <p className="pt-1 text-center">Head Agronomist, KITA Agritech</p>
              <p className="py-2">
                <strong>PRC License No:</strong> {farmPlan?.head_agronomist_prc_number}
              </p>
              <p className="">
                <strong>Valid Until:</strong> {farmPlan?.head_agronomist_valid_until}
              </p>
            </div>

            <div className="flex flex-col">
              <p className="mb-6 py-2">Noted by:</p>

              <span className="mx-4 mt-4 inline-block border-b border-black px-8 text-center"></span>
              <p className="pt-1 text-center">Municipal / City Agriculture Officer</p>
              <p className="py-2">
                <strong>PRC License No:</strong>{' '}
                <span className="mt-2 inline-block  w-24 border-b border-black text-center"></span>
              </p>
              <p className="">
                <strong>Valid Until:</strong>{' '}
                <span className="inline-block w-24  border-b border-black text-center"></span>
              </p>
            </div>
          </div>
          <div className="w-full py-14 text-center text-xs font-medium">
            I hereby agree to this Farm Plan and Budget in relation to my farm loan application with LANDBANK AGRISENSO
            PLUS.
          </div>

          <span className="mx-4 inline-block border-b border-black px-8 text-center text-xs">
            {farmPlan?.head_agronomist_name}
          </span>
          <p className="pt-1 text-center text-xs font-semibold">Farmer Partner</p>
        </div>

        <div className="flex min-w-[40%] flex-col space-y-4 text-xs">
          <div className="border border-gray-400 ">
            <div className="bg-gray-300 p-2 text-center font-medium">Cost Summary</div>
            <div className="grid grid-cols-3 border border-t-gray-400">
              <div className="col-span-2 border border-r-gray-400 p-2">Farm Inputs + Contingency</div>
              <div className="border border-b-gray-400 p-2">{formatCurrency(costSummary.farmInputsContingency)}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">Cash Requirements</div>
              <div className="p-2">{formatCurrency(costSummary.cashRequirements)}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">Farmer&apos;s Equity (FE)</div>
              <div className="border border-t-gray-400 p-2">{formatCurrency(costSummary.farmersEquity)}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 bg-gray-300 p-2 font-medium">
                Total Production Costs (TPC)
              </div>
              <div className="border border-t-gray-400 p-2">{formatCurrency(costSummary.totalProductionCosts)}</div>
            </div>
          </div>

          <div className="border border-gray-400 ">
            <div className="bg-gray-300 p-2 text-center font-medium">Estimated Loan Repayment Calculator</div>
            <div className="grid grid-cols-3 border border-t-gray-400">
              <div className="col-span-2 border border-r-gray-400 p-2">Total LBP Share / Principal (TPC - FE)</div>
              <div className="border border-b-gray-400 p-2">{formatCurrency(costSummary.lbpShare)}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">Interest Rate (% p.a.)</div>
              <div className="p-2">{farmPlan?.interest_rate}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">Number of Months / Tenor</div>
              <div className="border border-t-gray-400 p-2">{farmPlan?.number_of_months_per_tenor}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">AOR per Month</div>
              <div className="border border-t-gray-400 p-2">{farmPlan?.aor_per_month}</div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 p-2">
                Estimated Computed Interest
              </div>
              <div className="border border-t-gray-400 p-2">
                {formatCurrency(costSummary?.estimatedComputedInterest)}
              </div>
              <div className="col-span-2 border border-r-gray-400 border-t-gray-400 bg-gray-300 p-2 font-medium">
                Principal + Interest
              </div>
              <div className="border border-t-gray-400 p-2">{formatCurrency(costSummary?.principalPlusInterest)}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Page2;
