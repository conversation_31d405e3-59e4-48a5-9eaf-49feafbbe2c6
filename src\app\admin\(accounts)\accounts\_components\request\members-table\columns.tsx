'use client';

import { useHookstate } from '@hookstate/core';
import { format } from 'date-fns';
import { CircleCheckIcon, CircleXIcon, EyeIcon, MoreHorizontal } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { DataTableColumnHeader } from '@/components/layout/table/table-column-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import { UserStatusLabels, UserStatusType } from '@/app/admin/marketplace/_components/Enums';
import { getUserType } from '@/lib/constants';

import { useFarmersRequest } from '../hooks/useFarmersRequest';

export const columns = [
  {
    id: 'account_name',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Account Name" />,
    accessorFn: (row) => {
      const type = getUserType(row.user_type);
      return `${row[type]?.first_name} ${row[type]?.last_name}`;
    },
  },
  {
    id: 'status',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Status" />,
    cell: ({ row }) => {
      const data = row.original;

      return (
        <div className="flex min-w-max items-center gap-3">
          <Badge className={UserStatusLabels[data.status].color}>
            <span>{UserStatusLabels[data.status].label}</span>
          </Badge>
        </div>
      );
    },
    accessorFn: (row) => {
      return UserStatusLabels[row.status].label;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    id: 'date_time_submitted',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Date & Time Submitted" />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{format(new Date(data.created_at), 'dd MMM yyyy | hh:mm a')}</div>;
    },
  },
  {
    accessorKey: 'email',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Email" />,
  },
  {
    id: 'mobile_no',
    header: ({ column }) => <DataTableColumnHeader column={column} title="Mobile No." />,
    cell: ({ row }) => {
      const data = row.original;
      return <div className="min-w-max">{data.farmer.mobile_number}</div>;
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => <Action row={row} />,
  },
];

const Action = ({ row }) => {
  const data = row.original;
  const router = useRouter();
  const { approveFarmerMutation, rejectFarmerMutation } = useFarmersRequest();

  const rejectConfirm = useHookstate(false);
  const approveConfirm = useHookstate(false);

  return (
    <>
      <div className="flex justify-end gap-2">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon">
              <MoreHorizontal className="size-5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end">
            {data.status === UserStatusType.PENDING_FROM_ADMIN && (
              <>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                {/* <DropdownMenuSeparator /> */}
                <DropdownMenuItem className="flex items-center" onClick={() => approveConfirm.set(true)}>
                  <CircleCheckIcon className="mr-2 size-4" />
                  <span>Approve</span>
                </DropdownMenuItem>

                <DropdownMenuItem className="flex items-center" onClick={() => rejectConfirm.set(true)}>
                  <CircleXIcon className="mr-2 size-4" />
                  <span>Reject</span>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
              </>
            )}

            <DropdownMenuItem
              className="flex items-center"
              onClick={() => {
                router.push(`/admin/account-info/?id=${data.id}`);
              }}
            >
              <EyeIcon className="mr-2 size-4" />
              <span>Account Info</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Deactivate Confirmation */}
        <AlertDialog open={rejectConfirm.value} onOpenChange={rejectConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will reject the account associated with the username: {data.email}. This process cannot be
                undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => rejectFarmerMutation.mutate(data.id)}>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Activate Confirmation */}
        <AlertDialog open={approveConfirm.value} onOpenChange={approveConfirm.set}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action will approve the account associated with the username: {data.email}. This process cannot be
                undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={() => approveFarmerMutation.mutate(data.id)}>Continue</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </>
  );
};
