'use client';

import { useHookstate } from '@hookstate/core';
import { VisuallyHidden } from '@radix-ui/react-visually-hidden';
import { CheckIcon, XIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { ButtonLoading } from '@/components/ButtonLoading';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { getUserType } from '@/lib/constants';
import useLoanApplication from '@/lib/hooks/useLoanApplication';
import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

export function LoanApplicationActionsDialog({ action = '' as 'approve' | 'reject', ...props }) {
  // use this if you want to control the alert dialog programatically
  const [open, setOpen] = useState(false);
  const { loanApplicationAction } = useLoanApplication();
  const loading = useHookstate(false);
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const isAdmin = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'admin';
  const isFinance = getUserType(gStateP['user'].value && gStateP['user']['user']['user_type'].value) === 'finance';

  const details = useHookstate(gStateP.selected.loanApplicationDetails);
  const creditScoreGroupId = useHookstate('');

  const onSubmit = async () => {
    try {
      loading.set(true);
      let updateData: any = {
        loanApplicationSubmissionId: details['id'].value,
      };

      if (action === 'approve') {
        if (details['financingCompanies'].length > 1) {
          updateData = {
            loanApplicationSubmissionId: details['id'].value,
            creditScoreGroupId: creditScoreGroupId.value,
          };
        } else {
          updateData = {
            loanApplicationSubmissionId: details['id'].value,
            creditScoreGroupId: details['financingCompanies'][0]['creditScoreGroup']['id'].value,
          };
        }
      }

      await loanApplicationAction(action, updateData);

      setOpen(false);
      router.back();
    } catch (error) {
      console.error(error);
    } finally {
      loading.set(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {action === 'approve' ? (
          <Button {...props} variant="success" disabled={loading.value}>
            <CheckIcon className="mr-2 size-4" />
            Completed
          </Button>
        ) : (
          <Button {...props} variant="destructive" disabled={loading.value}>
            <XIcon className="mr-2 size-4" />
            Widraw
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="font-sans sm:max-w-xl">
        <DialogHeader>
          <VisuallyHidden>
            <DialogTitle>Confirmation</DialogTitle>
          </VisuallyHidden>
        </DialogHeader>

        <div className="grid p-8">
          <div>
            <img className="mx-auto" src="/assets/undraw/confirm.png" alt="" />
          </div>

          <div className="pb-6 text-center">
            <div className="mb-2 mt-6 text-xl font-bold">Confirmation</div>
            <div>{`Are you sure you want to ${action} this loan?`}</div>

            <div className={cn(details['financingCompanies'].length > 1 ? 'block' : 'hidden')}>
              <div className="mt-4">Choose the financing company that approved this loan</div>

              <div className="mt-4 flex justify-center">
                <Select
                  value={creditScoreGroupId.value}
                  onValueChange={(v) => {
                    creditScoreGroupId.set(v);
                  }}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Select financing company" />
                  </SelectTrigger>
                  <SelectContent>
                    {details['financingCompanies'].value.map((v) => (
                      <SelectItem key={v.id} value={`${v.creditScoreGroup.id}`}>
                        {v.creditScoreGroup.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>

        {/* {action === 'reject' && (
            <div className="grid gap-4 pb-6 pt-2">
              <div className="grid gap-2">
                <Label htmlFor="remarks">Remarks</Label>
                <Textarea
                  {...register('remarks', {
                    required: 'Remarks is required',
                  })}
                  className={cn(errors.remarks && 'border-red-500 focus-visible:ring-red-500')}
                  placeholder={`Enter the reason why you want to ${action} this request.`}
                />
                {errors.remarks && <p className="form-error">{`${errors.remarks.message}`}</p>}
              </div>
            </div>
          )} */}

        <DialogFooter className="gap-3 sm:justify-between sm:gap-0">
          <DialogClose asChild>
            <Button className="px-12" type="button" variant="outline">
              Cancel
            </Button>
          </DialogClose>

          {loading.value ? (
            <ButtonLoading />
          ) : (
            action && (
              <Button className="px-12 capitalize" type="button" onClick={onSubmit}>
                {action === 'approve' ? 'Completed' : 'Widraw'}
              </Button>
            )
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
