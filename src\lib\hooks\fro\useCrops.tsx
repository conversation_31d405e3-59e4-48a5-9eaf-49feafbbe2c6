'use client';

import { toast } from 'sonner';
import { z } from 'zod';

import axios from '@/lib/api';

// Validation schema for FRO crop creation (simplified)
export const FROCropSingleSchema = z.object({
  name: z
    .string()
    .min(2, 'Crop name is required')
    .max(50, 'Crop name is too long')
    .refine((val) => /^[a-zA-Z][a-zA-Z\s]*[a-zA-Z]$|^[a-zA-Z]$/.test(val), {
      message: 'Crop name must contain only letters and spaces',
    }),
});

export type FROCropSingleType = z.infer<typeof FROCropSingleSchema>;

export default function useFROCrops() {
  const addCropSingle = async (data: FROCropSingleType) => {
    console.log('FRO addCropSingle: ', data);

    try {
      const response = await axios
        .post('/fro/crops/create/single', data, {
          headers: {
            'Content-Type': 'application/json',
          },
        })
        .then((res) => res.data);

      console.log('FRO addCropSingle response: ', response);

      toast.success('Success', {
        description: `${data.name} has been added successfully`,
        duration: 3000,
      });

      return response;
    } catch (e) {
      const error = e?.response?.data?.message || e.message;
      console.error('FRO addCropSingle error: ', error);

      toast.error('Oops! Something went wrong', {
        description: error,
        duration: 3000,
      });

      throw e;
    }
  };

  return {
    addCropSingle,
  };
}
