'use client';

import { useHookstate } from '@hookstate/core';
import { useEffect } from 'react';

import { useGlobalState } from '@/lib/store';

import { LandbankRequirementType } from '../../../constants';
import { LandbankRequirementsTable } from './landbank-reqts-table';
import { columns } from './landbank-reqts-table/columns';

export default function LandbankReqts() {
  const gState = useGlobalState();
  const data = useHookstate(gState.selected.accountInfo['info']);
  const farmerRequirements = data.farmer.farmerLandbankRequirements.value || [];
  const allRequirements = Object.values(LandbankRequirementType).map(
    (type) => farmerRequirements.find((r: any) => r.type === type) || { type },
  );

  useEffect(() => {
    return () => {
      gState.landbankReqs.selectedAttachments.set([]);
      gState.landbankReqs.selectAll.set(false);
    };
  }, []);

  return (
    <div className="px-6 py-8">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-bold">Landbank Requirements</h2>
      </div>

      <LandbankRequirementsTable columns={columns} data={allRequirements} />
    </div>
  );
}
