'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import { toast } from 'sonner';

import useFinance from '@/lib/hooks/admin/useFinance';

export default function FetchDetails() {
  const params = useSearchParams();
  const detailsId = params.get('id');
  const farmerId = params.get('fid');
  const creditScoreId = params.get('csid');

  const { getReqDetails, getCreditScore } = useFinance();
  const router = useRouter();

  useEffect(() => {
    if (detailsId) {
      getReqDetails(detailsId);

      if (creditScoreId && farmerId) getCreditScore(farmerId, creditScoreId);
    } else {
      toast.error('Oops! Something went wrong', {
        description: 'Invalid ID, Please try again',
      });
      router.back();
    }
  }, []);

  return null;
}
